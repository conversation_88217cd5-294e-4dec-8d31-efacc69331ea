{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"10\",\n  height: \"14\",\n  x: \"3\",\n  y: \"8\",\n  rx: \"2\",\n  key: \"1vrsiq\"\n}], [\"path\", {\n  d: \"M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4\",\n  key: \"1j4zmg\"\n}], [\"path\", {\n  d: \"M8 18h.01\",\n  key: \"lrp35t\"\n}]];\nconst TabletSmartphone = createLucideIcon(\"tablet-smartphone\", __iconNode);\nexport { __iconNode, TabletSmartphone as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "key", "d", "TabletSmartphone", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\tablet-smartphone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '10', height: '14', x: '3', y: '8', rx: '2', key: '1vrsiq' }],\n  ['path', { d: 'M5 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2h-2.4', key: '1j4zmg' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n];\n\n/**\n * @component @name TabletSmartphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTAiIGhlaWdodD0iMTQiIHg9IjMiIHk9IjgiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik01IDRhMiAyIDAgMCAxIDItMmgxMmEyIDIgMCAwIDEgMiAydjE2YTIgMiAwIDAgMS0yIDJoLTIuNCIgLz4KICA8cGF0aCBkPSJNOCAxOGguMDEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/tablet-smartphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TabletSmartphone = createLucideIcon('tablet-smartphone', __iconNode);\n\nexport default TabletSmartphone;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GAC9E,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA6DD,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAaD,GAAA,EAAK;AAAA,CAAU,EAC5C;AAaA,MAAME,gBAAA,GAAmBC,gBAAA,CAAiB,qBAAqBT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}