{"ast": null, "code": "import { transformProps, isCSSVariableName, getValueAsType, numberValueTypes } from 'motion-dom';\nimport { buildTransform } from './build-transform.mjs';\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n  const {\n    style,\n    vars,\n    transformOrigin\n  } = state;\n  // Track whether we encounter any transform or transformOrigin values.\n  let hasTransform = false;\n  let hasTransformOrigin = false;\n  /**\n   * Loop over all our latest animated values and decide whether to handle them\n   * as a style or CSS variable.\n   *\n   * Transforms and transform origins are kept separately for further processing.\n   */\n  for (const key in latestValues) {\n    const value = latestValues[key];\n    if (transformProps.has(key)) {\n      // If this is a transform, flag to enable further transform processing\n      hasTransform = true;\n      continue;\n    } else if (isCSSVariableName(key)) {\n      vars[key] = value;\n      continue;\n    } else {\n      // Convert the value to its default value type, ie 0 -> \"0px\"\n      const valueAsType = getValueAsType(value, numberValueTypes[key]);\n      if (key.startsWith(\"origin\")) {\n        // If this is a transform origin, flag and enable further transform-origin processing\n        hasTransformOrigin = true;\n        transformOrigin[key] = valueAsType;\n      } else {\n        style[key] = valueAsType;\n      }\n    }\n  }\n  if (!latestValues.transform) {\n    if (hasTransform || transformTemplate) {\n      style.transform = buildTransform(latestValues, state.transform, transformTemplate);\n    } else if (style.transform) {\n      /**\n       * If we have previously created a transform but currently don't have any,\n       * reset transform style to none.\n       */\n      style.transform = \"none\";\n    }\n  }\n  /**\n   * Build a transformOrigin style. Uses the same defaults as the browser for\n   * undefined origins.\n   */\n  if (hasTransformOrigin) {\n    const {\n      originX = \"50%\",\n      originY = \"50%\",\n      originZ = 0\n    } = transformOrigin;\n    style.transformOrigin = `${originX} ${originY} ${originZ}`;\n  }\n}\nexport { buildHTMLStyles };", "map": {"version": 3, "names": ["transformProps", "isCSSVariableName", "getValueAsType", "numberValueTypes", "buildTransform", "buildHTMLStyles", "state", "latestValues", "transformTemplate", "style", "vars", "transform<PERSON><PERSON>in", "hasTransform", "hasTransformOrigin", "key", "value", "has", "valueAsType", "startsWith", "transform", "originX", "originY", "originZ"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs"], "sourcesContent": ["import { transformProps, isCSSVariableName, getValueAsType, numberValueTypes } from 'motion-dom';\nimport { buildTransform } from './build-transform.mjs';\n\nfunction buildHTMLStyles(state, latestValues, transformTemplate) {\n    const { style, vars, transformOrigin } = state;\n    // Track whether we encounter any transform or transformOrigin values.\n    let hasTransform = false;\n    let hasTransformOrigin = false;\n    /**\n     * Loop over all our latest animated values and decide whether to handle them\n     * as a style or CSS variable.\n     *\n     * Transforms and transform origins are kept separately for further processing.\n     */\n    for (const key in latestValues) {\n        const value = latestValues[key];\n        if (transformProps.has(key)) {\n            // If this is a transform, flag to enable further transform processing\n            hasTransform = true;\n            continue;\n        }\n        else if (isCSSVariableName(key)) {\n            vars[key] = value;\n            continue;\n        }\n        else {\n            // Convert the value to its default value type, ie 0 -> \"0px\"\n            const valueAsType = getValueAsType(value, numberValueTypes[key]);\n            if (key.startsWith(\"origin\")) {\n                // If this is a transform origin, flag and enable further transform-origin processing\n                hasTransformOrigin = true;\n                transformOrigin[key] =\n                    valueAsType;\n            }\n            else {\n                style[key] = valueAsType;\n            }\n        }\n    }\n    if (!latestValues.transform) {\n        if (hasTransform || transformTemplate) {\n            style.transform = buildTransform(latestValues, state.transform, transformTemplate);\n        }\n        else if (style.transform) {\n            /**\n             * If we have previously created a transform but currently don't have any,\n             * reset transform style to none.\n             */\n            style.transform = \"none\";\n        }\n    }\n    /**\n     * Build a transformOrigin style. Uses the same defaults as the browser for\n     * undefined origins.\n     */\n    if (hasTransformOrigin) {\n        const { originX = \"50%\", originY = \"50%\", originZ = 0, } = transformOrigin;\n        style.transformOrigin = `${originX} ${originY} ${originZ}`;\n    }\n}\n\nexport { buildHTMLStyles };\n"], "mappings": "AAAA,SAASA,cAAc,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AAChG,SAASC,cAAc,QAAQ,uBAAuB;AAEtD,SAASC,eAAeA,CAACC,KAAK,EAAEC,YAAY,EAAEC,iBAAiB,EAAE;EAC7D,MAAM;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAgB,CAAC,GAAGL,KAAK;EAC9C;EACA,IAAIM,YAAY,GAAG,KAAK;EACxB,IAAIC,kBAAkB,GAAG,KAAK;EAC9B;AACJ;AACA;AACA;AACA;AACA;EACI,KAAK,MAAMC,GAAG,IAAIP,YAAY,EAAE;IAC5B,MAAMQ,KAAK,GAAGR,YAAY,CAACO,GAAG,CAAC;IAC/B,IAAId,cAAc,CAACgB,GAAG,CAACF,GAAG,CAAC,EAAE;MACzB;MACAF,YAAY,GAAG,IAAI;MACnB;IACJ,CAAC,MACI,IAAIX,iBAAiB,CAACa,GAAG,CAAC,EAAE;MAC7BJ,IAAI,CAACI,GAAG,CAAC,GAAGC,KAAK;MACjB;IACJ,CAAC,MACI;MACD;MACA,MAAME,WAAW,GAAGf,cAAc,CAACa,KAAK,EAAEZ,gBAAgB,CAACW,GAAG,CAAC,CAAC;MAChE,IAAIA,GAAG,CAACI,UAAU,CAAC,QAAQ,CAAC,EAAE;QAC1B;QACAL,kBAAkB,GAAG,IAAI;QACzBF,eAAe,CAACG,GAAG,CAAC,GAChBG,WAAW;MACnB,CAAC,MACI;QACDR,KAAK,CAACK,GAAG,CAAC,GAAGG,WAAW;MAC5B;IACJ;EACJ;EACA,IAAI,CAACV,YAAY,CAACY,SAAS,EAAE;IACzB,IAAIP,YAAY,IAAIJ,iBAAiB,EAAE;MACnCC,KAAK,CAACU,SAAS,GAAGf,cAAc,CAACG,YAAY,EAAED,KAAK,CAACa,SAAS,EAAEX,iBAAiB,CAAC;IACtF,CAAC,MACI,IAAIC,KAAK,CAACU,SAAS,EAAE;MACtB;AACZ;AACA;AACA;MACYV,KAAK,CAACU,SAAS,GAAG,MAAM;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIN,kBAAkB,EAAE;IACpB,MAAM;MAAEO,OAAO,GAAG,KAAK;MAAEC,OAAO,GAAG,KAAK;MAAEC,OAAO,GAAG;IAAG,CAAC,GAAGX,eAAe;IAC1EF,KAAK,CAACE,eAAe,GAAG,GAAGS,OAAO,IAAIC,OAAO,IAAIC,OAAO,EAAE;EAC9D;AACJ;AAEA,SAASjB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}