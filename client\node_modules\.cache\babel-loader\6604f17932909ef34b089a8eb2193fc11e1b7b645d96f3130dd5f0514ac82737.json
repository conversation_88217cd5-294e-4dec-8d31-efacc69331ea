{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hotel mangement\\\\client\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { Search, Calendar, Users, Shield, MapPin, Star, ChevronRight, Bed } from 'lucide-react';\nimport { hotelInfo } from '../data/mockData';\nimport { useBooking } from '../contexts/BookingContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const [searchData, setSearchData] = useState({\n    roomType: '',\n    checkIn: '',\n    checkOut: '',\n    guests: 1\n  });\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setSearchData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSearch = e => {\n    e.preventDefault();\n    // Navigate to rooms page with search parameters\n    console.log('Search data:', searchData);\n  };\n  const features = [{\n    icon: Shield,\n    title: \"Secure Payments\",\n    description: \"100% secure payment processing with SSL encryption\"\n  }, {\n    icon: MapPin,\n    title: \"Prime Location\",\n    description: \"Located in the heart of the city with easy access to attractions\"\n  }, {\n    icon: Star,\n    title: \"4.8★ Rated\",\n    description: \"Highly rated by thousands of satisfied guests\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"relative h-screen flex items-center justify-center overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-cover bg-center bg-no-repeat\",\n        style: {\n          backgroundImage: \"url('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1920&h=1080&fit=crop')\"\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-black/40\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl md:text-6xl font-bold mb-6\",\n            children: hotelInfo.tagline\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl md:text-2xl mb-8 text-gray-200\",\n            children: [\"Discover luxury and comfort at \", hotelInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.8,\n              delay: 0.2\n            },\n            className: \"bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl max-w-4xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"form\", {\n              onSubmit: handleSearch,\n              className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Room Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"roomType\",\n                  value: searchData.roomType,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Rooms\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"standard\",\n                    children: \"Standard\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"deluxe\",\n                    children: \"Deluxe\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"suite\",\n                    children: \"Suite\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 96,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Check-in\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"checkIn\",\n                  value: searchData.checkIn,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Check-out\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"date\",\n                  name: \"checkOut\",\n                  value: searchData.checkOut,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"text-sm font-medium text-gray-700\",\n                  children: \"Guests\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  name: \"guests\",\n                  value: searchData.guests,\n                  onChange: handleInputChange,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\",\n                  children: [1, 2, 3, 4, 5, 6].map(num => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: num,\n                    children: [num, \" Guest\", num > 1 ? 's' : '']\n                  }, num, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:col-span-4 flex justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"submit\",\n                  className: \"inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200\",\n                  children: [/*#__PURE__*/_jsxDEV(Search, {\n                    className: \"h-5 w-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this), \"Search Rooms\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-gray-50 dark:bg-gray-900\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"text-center mb-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\",\n            children: \"Why Choose SereneStay?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-gray-600 dark:text-gray-300\",\n            children: \"Experience the perfect blend of luxury, comfort, and convenience\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"text-center p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full mb-6\",\n              children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                className: \"h-8 w-8 text-primary-600 dark:text-primary-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 dark:text-white mb-4\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 dark:text-gray-300\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this)]\n          }, feature.title, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-20 bg-primary-600 dark:bg-primary-700\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n            children: \"Ready to Experience Luxury?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xl text-primary-100 mb-8\",\n            children: \"Book your stay today and discover what makes SereneStay special\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/rooms\",\n              className: \"inline-flex items-center px-8 py-3 bg-white text-primary-600 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n              children: [/*#__PURE__*/_jsxDEV(Bed, {\n                className: \"h-5 w-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), \"View Rooms\", /*#__PURE__*/_jsxDEV(ChevronRight, {\n                className: \"h-5 w-5 ml-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/contact\",\n              className: \"inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:text-primary-600 transition-colors duration-200\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"qmP34mmMrASunDbJlHIRnCoyyCE=\");\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Link", "useNavigate", "Search", "Calendar", "Users", "Shield", "MapPin", "Star", "ChevronRight", "Bed", "hotelInfo", "useBooking", "jsxDEV", "_jsxDEV", "Home", "_s", "searchData", "setSearchData", "roomType", "checkIn", "checkOut", "guests", "handleInputChange", "e", "name", "value", "target", "prev", "handleSearch", "preventDefault", "console", "log", "features", "icon", "title", "description", "className", "children", "style", "backgroundImage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "y", "animate", "transition", "duration", "tagline", "delay", "onSubmit", "onChange", "type", "map", "num", "whileInView", "viewport", "once", "feature", "index", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/src/pages/Home.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Link, useNavigate } from 'react-router-dom';\nimport {\n  Search,\n  Calendar,\n  Users,\n  Shield,\n  MapPin,\n  Star,\n  ChevronRight,\n  Bed\n} from 'lucide-react';\nimport { hotelInfo } from '../data/mockData';\nimport { useBooking } from '../contexts/BookingContext';\n\nconst Home = () => {\n  const [searchData, setSearchData] = useState({\n    roomType: '',\n    checkIn: '',\n    checkOut: '',\n    guests: 1\n  });\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setSearchData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSearch = (e) => {\n    e.preventDefault();\n    // Navigate to rooms page with search parameters\n    console.log('Search data:', searchData);\n  };\n\n  const features = [\n    {\n      icon: Shield,\n      title: \"Secure Payments\",\n      description: \"100% secure payment processing with SSL encryption\"\n    },\n    {\n      icon: MapPin,\n      title: \"Prime Location\",\n      description: \"Located in the heart of the city with easy access to attractions\"\n    },\n    {\n      icon: Star,\n      title: \"4.8★ Rated\",\n      description: \"Highly rated by thousands of satisfied guests\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative h-screen flex items-center justify-center overflow-hidden\">\n        {/* Background Image */}\n        <div \n          className=\"absolute inset-0 bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: \"url('https://images.unsplash.com/photo-1566073771259-6a8506099945?w=1920&h=1080&fit=crop')\"\n          }}\n        >\n          <div className=\"absolute inset-0 bg-black/40\"></div>\n        </div>\n\n        {/* Hero Content */}\n        <div className=\"relative z-10 text-center text-white px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"max-w-4xl mx-auto\"\n          >\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              {hotelInfo.tagline}\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 text-gray-200\">\n              Discover luxury and comfort at {hotelInfo.name}\n            </p>\n            \n            {/* Search Bar */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl max-w-4xl mx-auto\"\n            >\n              <form onSubmit={handleSearch} className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">Room Type</label>\n                  <select\n                    name=\"roomType\"\n                    value={searchData.roomType}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\"\n                  >\n                    <option value=\"\">All Rooms</option>\n                    <option value=\"standard\">Standard</option>\n                    <option value=\"deluxe\">Deluxe</option>\n                    <option value=\"suite\">Suite</option>\n                  </select>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">Check-in</label>\n                  <input\n                    type=\"date\"\n                    name=\"checkIn\"\n                    value={searchData.checkIn}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\"\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">Check-out</label>\n                  <input\n                    type=\"date\"\n                    name=\"checkOut\"\n                    value={searchData.checkOut}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\"\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium text-gray-700\">Guests</label>\n                  <select\n                    name=\"guests\"\n                    value={searchData.guests}\n                    onChange={handleInputChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-gray-900\"\n                  >\n                    {[1, 2, 3, 4, 5, 6].map(num => (\n                      <option key={num} value={num}>{num} Guest{num > 1 ? 's' : ''}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div className=\"md:col-span-4 flex justify-center\">\n                  <button\n                    type=\"submit\"\n                    className=\"inline-flex items-center px-8 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200\"\n                  >\n                    <Search className=\"h-5 w-5 mr-2\" />\n                    Search Rooms\n                  </button>\n                </div>\n              </form>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-20 bg-gray-50 dark:bg-gray-900\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n              Why Choose SereneStay?\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300\">\n              Experience the perfect blend of luxury, comfort, and convenience\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center p-8 bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300\"\n              >\n                <div className=\"inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900/20 rounded-full mb-6\">\n                  <feature.icon className=\"h-8 w-8 text-primary-600 dark:text-primary-400\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                  {feature.title}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  {feature.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-primary-600 dark:bg-primary-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n              Ready to Experience Luxury?\n            </h2>\n            <p className=\"text-xl text-primary-100 mb-8\">\n              Book your stay today and discover what makes SereneStay special\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link\n                to=\"/rooms\"\n                className=\"inline-flex items-center px-8 py-3 bg-white text-primary-600 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200\"\n              >\n                <Bed className=\"h-5 w-5 mr-2\" />\n                View Rooms\n                <ChevronRight className=\"h-5 w-5 ml-2\" />\n              </Link>\n              <Link\n                to=\"/contact\"\n                className=\"inline-flex items-center px-8 py-3 border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:text-primary-600 transition-colors duration-200\"\n              >\n                Contact Us\n              </Link>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,IAAI,EACJC,YAAY,EACZC,GAAG,QACE,cAAc;AACrB,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,UAAU,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC;IAC3CoB,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,EAAE;IACXC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCT,aAAa,CAACU,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMG,YAAY,GAAIL,CAAC,IAAK;IAC1BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClB;IACAC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEf,UAAU,CAAC;EACzC,CAAC;EAED,MAAMgB,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE5B,MAAM;IACZ6B,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE3B,MAAM;IACZ4B,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE1B,IAAI;IACV2B,KAAK,EAAE,YAAY;IACnBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEtB,OAAA;IAAKuB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAE3BxB,OAAA;MAASuB,SAAS,EAAC,oEAAoE;MAAAC,QAAA,gBAErFxB,OAAA;QACEuB,SAAS,EAAC,kDAAkD;QAC5DE,KAAK,EAAE;UACLC,eAAe,EAAE;QACnB,CAAE;QAAAF,QAAA,eAEFxB,OAAA;UAAKuB,SAAS,EAAC;QAA8B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAGN9B,OAAA;QAAKuB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,eACxExB,OAAA,CAACd,MAAM,CAAC6C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9Bd,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BxB,OAAA;YAAIuB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAChD3B,SAAS,CAACyC;UAAO;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACL9B,OAAA;YAAGuB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,GAAC,iCACrB,EAAC3B,SAAS,CAACc,IAAI;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAGJ9B,OAAA,CAACd,MAAM,CAAC6C,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAE;YAAI,CAAE;YAC1ChB,SAAS,EAAC,2EAA2E;YAAAC,QAAA,eAErFxB,OAAA;cAAMwC,QAAQ,EAAEzB,YAAa;cAACQ,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAC7ExB,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxB,OAAA;kBAAOuB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtE9B,OAAA;kBACEW,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAET,UAAU,CAACE,QAAS;kBAC3BoC,QAAQ,EAAEhC,iBAAkB;kBAC5Bc,SAAS,EAAC,+HAA+H;kBAAAC,QAAA,gBAEzIxB,OAAA;oBAAQY,KAAK,EAAC,EAAE;oBAAAY,QAAA,EAAC;kBAAS;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnC9B,OAAA;oBAAQY,KAAK,EAAC,UAAU;oBAAAY,QAAA,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1C9B,OAAA;oBAAQY,KAAK,EAAC,QAAQ;oBAAAY,QAAA,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC9B,OAAA;oBAAQY,KAAK,EAAC,OAAO;oBAAAY,QAAA,EAAC;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN9B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxB,OAAA;kBAAOuB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrE9B,OAAA;kBACE0C,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,SAAS;kBACdC,KAAK,EAAET,UAAU,CAACG,OAAQ;kBAC1BmC,QAAQ,EAAEhC,iBAAkB;kBAC5Bc,SAAS,EAAC;gBAA+H;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxB,OAAA;kBAAOuB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtE9B,OAAA;kBACE0C,IAAI,EAAC,MAAM;kBACX/B,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAET,UAAU,CAACI,QAAS;kBAC3BkC,QAAQ,EAAEhC,iBAAkB;kBAC5Bc,SAAS,EAAC;gBAA+H;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1I,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9B,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBxB,OAAA;kBAAOuB,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnE9B,OAAA;kBACEW,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAET,UAAU,CAACK,MAAO;kBACzBiC,QAAQ,EAAEhC,iBAAkB;kBAC5Bc,SAAS,EAAC,+HAA+H;kBAAAC,QAAA,EAExI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACmB,GAAG,CAACC,GAAG,iBACzB5C,OAAA;oBAAkBY,KAAK,EAAEgC,GAAI;oBAAApB,QAAA,GAAEoB,GAAG,EAAC,QAAM,EAACA,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA,GAA/CA,GAAG;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAqD,CACtE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAEN9B,OAAA;gBAAKuB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,eAChDxB,OAAA;kBACE0C,IAAI,EAAC,QAAQ;kBACbnB,SAAS,EAAC,yIAAyI;kBAAAC,QAAA,gBAEnJxB,OAAA,CAACX,MAAM;oBAACkC,SAAS,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAErC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV9B,OAAA;MAASuB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eACpDxB,OAAA;QAAKuB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDxB,OAAA,CAACd,MAAM,CAAC6C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBxB,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAE7BxB,OAAA;YAAIuB,SAAS,EAAC,mEAAmE;YAAAC,QAAA,EAAC;UAElF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9B,OAAA;YAAGuB,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAExD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEb9B,OAAA;UAAKuB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDL,QAAQ,CAACwB,GAAG,CAAC,CAACK,OAAO,EAAEC,KAAK,kBAC3BjD,OAAA,CAACd,MAAM,CAAC6C,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BW,WAAW,EAAE;cAAEZ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEE,KAAK,EAAEU,KAAK,GAAG;YAAI,CAAE;YAClDH,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBxB,SAAS,EAAC,gHAAgH;YAAAC,QAAA,gBAE1HxB,OAAA;cAAKuB,SAAS,EAAC,2GAA2G;cAAAC,QAAA,eACxHxB,OAAA,CAACgD,OAAO,CAAC5B,IAAI;gBAACG,SAAS,EAAC;cAAgD;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC,eACN9B,OAAA;cAAIuB,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACrEwB,OAAO,CAAC3B;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACL9B,OAAA;cAAGuB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAC5CwB,OAAO,CAAC1B;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA,GAfCkB,OAAO,CAAC3B,KAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBR,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGV9B,OAAA;MAASuB,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eAC3DxB,OAAA;QAAKuB,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eACjExB,OAAA,CAACd,MAAM,CAAC6C,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BW,WAAW,EAAE;YAAEZ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BS,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UAAAvB,QAAA,gBAEzBxB,OAAA;YAAIuB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAE/D;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9B,OAAA;YAAGuB,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAC;UAE7C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ9B,OAAA;YAAKuB,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DxB,OAAA,CAACb,IAAI;cACH+D,EAAE,EAAC,QAAQ;cACX3B,SAAS,EAAC,qIAAqI;cAAAC,QAAA,gBAE/IxB,OAAA,CAACJ,GAAG;gBAAC2B,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAEhC,eAAA9B,OAAA,CAACL,YAAY;gBAAC4B,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACP9B,OAAA,CAACb,IAAI;cACH+D,EAAE,EAAC,UAAU;cACb3B,SAAS,EAAC,iKAAiK;cAAAC,QAAA,EAC5K;YAED;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA9NID,IAAI;AAAAkD,EAAA,GAAJlD,IAAI;AAgOV,eAAeA,IAAI;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}