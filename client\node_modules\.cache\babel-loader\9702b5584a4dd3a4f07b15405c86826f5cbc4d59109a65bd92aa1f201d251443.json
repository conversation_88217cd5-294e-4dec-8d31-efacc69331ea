{"ast": null, "code": "import { formatErrorMessage } from './format-error-message.mjs';\nconst warned = new Set();\nfunction hasWarned(message) {\n  return warned.has(message);\n}\nfunction warnOnce(condition, message, errorCode) {\n  if (condition || warned.has(message)) return;\n  console.warn(formatErrorMessage(message, errorCode));\n  warned.add(message);\n}\nexport { hasWarned, warnOnce };", "map": {"version": 3, "names": ["formatErrorMessage", "warned", "Set", "hasWarned", "message", "has", "warnOnce", "condition", "errorCode", "console", "warn", "add"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["import { formatErrorMessage } from './format-error-message.mjs';\n\nconst warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, errorCode) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(formatErrorMessage(message, errorCode));\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,4BAA4B;AAE/D,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;AACxB,SAASC,SAASA,CAACC,OAAO,EAAE;EACxB,OAAOH,MAAM,CAACI,GAAG,CAACD,OAAO,CAAC;AAC9B;AACA,SAASE,QAAQA,CAACC,SAAS,EAAEH,OAAO,EAAEI,SAAS,EAAE;EAC7C,IAAID,SAAS,IAAIN,MAAM,CAACI,GAAG,CAACD,OAAO,CAAC,EAChC;EACJK,OAAO,CAACC,IAAI,CAACV,kBAAkB,CAACI,OAAO,EAAEI,SAAS,CAAC,CAAC;EACpDP,MAAM,CAACU,GAAG,CAACP,OAAO,CAAC;AACvB;AAEA,SAASD,SAAS,EAAEG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}