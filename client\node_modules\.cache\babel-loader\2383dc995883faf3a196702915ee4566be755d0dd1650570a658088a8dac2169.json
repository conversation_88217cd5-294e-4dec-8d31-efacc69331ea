{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"path\", {\n  d: \"m16 16 4-4-4-4\",\n  key: \"1js579\"\n}], [\"path\", {\n  d: \"m8 8-4 4 4 4\",\n  key: \"1whems\"\n}]];\nconst SeparatorVertical = createLucideIcon(\"separator-vertical\", __iconNode);\nexport { __iconNode, SeparatorVertical as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SeparatorVertical", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\separator-vertical.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3v18', key: '108xh3' }],\n  ['path', { d: 'm16 16 4-4-4-4', key: '1js579' }],\n  ['path', { d: 'm8 8-4 4 4 4', key: '1whems' }],\n];\n\n/**\n * @component @name SeparatorVertical\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3YxOCIgLz4KICA8cGF0aCBkPSJtMTYgMTYgNC00LTQtNCIgLz4KICA8cGF0aCBkPSJtOCA4LTQgNCA0IDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/separator-vertical\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SeparatorVertical = createLucideIcon('separator-vertical', __iconNode);\n\nexport default SeparatorVertical;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAkBC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAgBC,GAAA,EAAK;AAAA,CAAU,EAC/C;AAaA,MAAMC,iBAAA,GAAoBC,gBAAA,CAAiB,sBAAsBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}