{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 11c-1.5 0-2.5.5-3 2\",\n  key: \"1fod00\"\n}], [\"path\", {\n  d: \"M4 6a2 2 0 0 0-2 2v4a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3a8 8 0 0 0-5 2 8 8 0 0 0-5-2z\",\n  key: \"d70hit\"\n}], [\"path\", {\n  d: \"M6 11c1.5 0 2.5.5 3 2\",\n  key: \"136fht\"\n}]];\nconst VenetianMask = createLucideIcon(\"venetian-mask\", __iconNode);\nexport { __iconNode, VenetianMask as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "VenetianMask", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\venetian-mask.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M18 11c-1.5 0-2.5.5-3 2', key: '1fod00' }],\n  [\n    'path',\n    {\n      d: 'M4 6a2 2 0 0 0-2 2v4a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3a8 8 0 0 0-5 2 8 8 0 0 0-5-2z',\n      key: 'd70hit',\n    },\n  ],\n  ['path', { d: 'M6 11c1.5 0 2.5.5 3 2', key: '136fht' }],\n];\n\n/**\n * @component @name VenetianMask\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggMTFjLTEuNSAwLTIuNS41LTMgMiIgLz4KICA8cGF0aCBkPSJNNCA2YTIgMiAwIDAgMC0yIDJ2NGE1IDUgMCAwIDAgNSA1IDggOCAwIDAgMSA1IDIgOCA4IDAgMCAxIDUtMiA1IDUgMCAwIDAgNS01VjhhMiAyIDAgMCAwLTItMmgtM2E4IDggMCAwIDAtNSAyIDggOCAwIDAgMC01LTJ6IiAvPgogIDxwYXRoIGQ9Ik02IDExYzEuNSAwIDIuNS41IDMgMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/venetian-mask\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst VenetianMask = createLucideIcon('venetian-mask', __iconNode);\n\nexport default VenetianMask;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA2BC,GAAA,EAAK;AAAA,CAAU,GACxD,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAyBC,GAAA,EAAK;AAAA,CAAU,EACxD;AAaA,MAAMC,YAAA,GAAeC,gBAAA,CAAiB,iBAAiBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}