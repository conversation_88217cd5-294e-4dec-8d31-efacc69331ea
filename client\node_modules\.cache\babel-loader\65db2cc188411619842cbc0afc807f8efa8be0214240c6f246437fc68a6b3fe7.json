{"ast": null, "code": "import { isMotionValue } from 'motion-dom';\nimport { Fragment, useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nfunction useRender(Component, props, ref, {\n  latestValues\n}, isStatic, forwardMotionProps = false) {\n  const useVisualProps = isSVGComponent(Component) ? useSVGProps : useHTMLProps;\n  const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n  const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n  const elementProps = Component !== Fragment ? {\n    ...filteredProps,\n    ...visualProps,\n    ref\n  } : {};\n  /**\n   * If component has been handed a motion value as its child,\n   * memoise its initial value and render that. Subsequent updates\n   * will be handled by the onChange handler\n   */\n  const {\n    children\n  } = props;\n  const renderedChildren = useMemo(() => isMotionValue(children) ? children.get() : children, [children]);\n  return createElement(Component, {\n    ...elementProps,\n    children: renderedChildren\n  });\n}\nexport { useRender };", "map": {"version": 3, "names": ["isMotionValue", "Fragment", "useMemo", "createElement", "useHTMLProps", "useSVGProps", "filterProps", "isSVGComponent", "useRender", "Component", "props", "ref", "latestValues", "isStatic", "forwardMotionProps", "useVisualProps", "visualProps", "filteredProps", "elementProps", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/render/dom/use-render.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { Fragment, useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nfunction useRender(Component, props, ref, { latestValues, }, isStatic, forwardMotionProps = false) {\n    const useVisualProps = isSVGComponent(Component)\n        ? useSVGProps\n        : useHTMLProps;\n    const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n    const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n    const elementProps = Component !== Fragment ? { ...filteredProps, ...visualProps, ref } : {};\n    /**\n     * If component has been handed a motion value as its child,\n     * memoise its initial value and render that. Subsequent updates\n     * will be handled by the onChange handler\n     */\n    const { children } = props;\n    const renderedChildren = useMemo(() => (isMotionValue(children) ? children.get() : children), [children]);\n    return createElement(Component, {\n        ...elementProps,\n        children: renderedChildren,\n    });\n}\n\nexport { useRender };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,QAAQ,OAAO;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,SAASC,SAASA,CAACC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAAEC;AAAc,CAAC,EAAEC,QAAQ,EAAEC,kBAAkB,GAAG,KAAK,EAAE;EAC/F,MAAMC,cAAc,GAAGR,cAAc,CAACE,SAAS,CAAC,GAC1CJ,WAAW,GACXD,YAAY;EAClB,MAAMY,WAAW,GAAGD,cAAc,CAACL,KAAK,EAAEE,YAAY,EAAEC,QAAQ,EAAEJ,SAAS,CAAC;EAC5E,MAAMQ,aAAa,GAAGX,WAAW,CAACI,KAAK,EAAE,OAAOD,SAAS,KAAK,QAAQ,EAAEK,kBAAkB,CAAC;EAC3F,MAAMI,YAAY,GAAGT,SAAS,KAAKR,QAAQ,GAAG;IAAE,GAAGgB,aAAa;IAAE,GAAGD,WAAW;IAAEL;EAAI,CAAC,GAAG,CAAC,CAAC;EAC5F;AACJ;AACA;AACA;AACA;EACI,MAAM;IAAEQ;EAAS,CAAC,GAAGT,KAAK;EAC1B,MAAMU,gBAAgB,GAAGlB,OAAO,CAAC,MAAOF,aAAa,CAACmB,QAAQ,CAAC,GAAGA,QAAQ,CAACE,GAAG,CAAC,CAAC,GAAGF,QAAS,EAAE,CAACA,QAAQ,CAAC,CAAC;EACzG,OAAOhB,aAAa,CAACM,SAAS,EAAE;IAC5B,GAAGS,YAAY;IACfC,QAAQ,EAAEC;EACd,CAAC,CAAC;AACN;AAEA,SAASZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}