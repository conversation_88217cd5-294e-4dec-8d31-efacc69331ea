@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Light theme colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;

  --yellow-400: #facc15;
  --yellow-500: #eab308;

  --red-500: #ef4444;
  --red-600: #dc2626;

  --purple-600: #9333ea;
  --purple-700: #7c3aed;

  --orange-600: #ea580c;
  --orange-700: #c2410c;

  --blue-600: #2563eb;
  --blue-700: #1d4ed8;

  /* Theme variables */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-muted: var(--gray-500);
  --border-color: var(--gray-200);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-muted: var(--gray-400);
  --border-color: var(--gray-700);
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.top-0 {
  top: 0;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
}

.overflow-hidden {
  overflow: hidden;
}

.rounded {
  border-radius: 0.375rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-full {
  border-radius: 9999px;
}

.shadow {
  box-shadow: var(--shadow);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.shadow-xl {
  box-shadow: var(--shadow-xl);
}

.transition {
  transition: all 0.3s ease;
}

.cursor-pointer {
  cursor: pointer;
}

/* Spacing */
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-12 { padding: 3rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }

.m-2 { margin: 0.5rem; }
.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.mx-auto { margin-left: auto; margin-right: auto; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.my-8 { margin-top: 2rem; margin-bottom: 2rem; }

.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.mt-16 { margin-top: 4rem; }

.mr-2 { margin-right: 0.5rem; }
.mr-4 { margin-right: 1rem; }

.ml-2 { margin-left: 0.5rem; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Colors */
.text-white { color: #ffffff; }
.text-gray-100 { color: var(--gray-100); }
.text-gray-200 { color: var(--gray-200); }
.text-gray-300 { color: var(--gray-300); }
.text-gray-400 { color: var(--gray-400); }
.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }

.text-primary-400 { color: var(--primary-400); }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }

.text-green-400 { color: var(--green-400); }
.text-green-500 { color: var(--green-500); }
.text-green-600 { color: var(--green-600); }

.text-yellow-400 { color: var(--yellow-400); }

.text-red-500 { color: var(--red-500); }

/* Background Colors */
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }
.bg-gray-200 { background-color: var(--gray-200); }
.bg-gray-700 { background-color: var(--gray-700); }
.bg-gray-800 { background-color: var(--gray-800); }
.bg-gray-900 { background-color: var(--gray-900); }

.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-600 { background-color: var(--primary-600); }
.bg-primary-700 { background-color: var(--primary-700); }

.bg-green-100 { background-color: #dcfce7; }
.bg-green-500 { background-color: var(--green-500); }
.bg-green-600 { background-color: var(--green-600); }

.bg-yellow-100 { background-color: #fef3c7; }
.bg-yellow-500 { background-color: var(--yellow-500); }

.bg-red-100 { background-color: #fee2e2; }
.bg-red-500 { background-color: var(--red-500); }

.bg-blue-500 { background-color: var(--blue-600); }
.bg-blue-600 { background-color: var(--blue-600); }

.bg-purple-500 { background-color: var(--purple-600); }
.bg-purple-600 { background-color: var(--purple-600); }

.bg-orange-600 { background-color: var(--orange-600); }
.bg-orange-700 { background-color: var(--orange-700); }

/* Borders */
.border { border: 1px solid var(--border-color); }
.border-2 { border: 2px solid var(--border-color); }
.border-gray-200 { border-color: var(--gray-200); }
.border-gray-300 { border-color: var(--gray-300); }
.border-gray-600 { border-color: var(--gray-600); }
.border-gray-700 { border-color: var(--gray-700); }
.border-gray-800 { border-color: var(--gray-800); }
.border-primary-600 { border-color: var(--primary-600); }
.border-white { border-color: #ffffff; }

/* Hover Effects */
.hover\:bg-gray-50:hover { background-color: var(--gray-50); }
.hover\:bg-gray-200:hover { background-color: var(--gray-200); }
.hover\:bg-gray-700:hover { background-color: var(--gray-700); }
.hover\:bg-gray-800:hover { background-color: var(--gray-800); }
.hover\:bg-primary-700:hover { background-color: var(--primary-700); }
.hover\:bg-primary-50:hover { background-color: var(--primary-50); }
.hover\:bg-green-700:hover { background-color: var(--green-600); }
.hover\:bg-orange-700:hover { background-color: var(--orange-700); }
.hover\:bg-white:hover { background-color: #ffffff; }

.hover\:text-primary-400:hover { color: var(--primary-400); }
.hover\:text-primary-600:hover { color: var(--primary-600); }
.hover\:text-gray-300:hover { color: var(--gray-300); }
.hover\:text-white:hover { color: #ffffff; }

.hover\:shadow-xl:hover { box-shadow: var(--shadow-xl); }
.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }

/* Grid System */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Flexbox */
.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn:focus {
  outline: 2px solid var(--primary-600);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-700);
}

.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.btn-secondary:hover {
  background-color: var(--gray-200);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-600);
  border: 1px solid var(--primary-600);
}

.btn-outline:hover {
  background-color: var(--primary-50);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

/* Cards */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-lg);
}

/* Forms */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-600);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  cursor: pointer;
}

.form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.875rem;
  resize: vertical;
  min-height: 120px;
}

/* Navbar Styles */
.navbar {
  position: sticky;
  top: 0;
  z-index: 50;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.dark .navbar {
  background-color: rgba(17, 24, 39, 0.95);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
}

.navbar-logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 700;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.navbar-link {
  position: relative;
  padding: 0.5rem 0.75rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-link:hover,
.navbar-link.active {
  color: var(--primary-600);
}

.navbar-link.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-600);
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.theme-toggle {
  padding: 0.5rem;
  border-radius: 0.5rem;
  background-color: var(--gray-100);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark .theme-toggle {
  background-color: var(--gray-800);
}

.theme-toggle:hover {
  background-color: var(--gray-200);
}

.dark .theme-toggle:hover {
  background-color: var(--gray-700);
}

/* Mobile Menu */
.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem;
  box-shadow: var(--shadow-lg);
}

.mobile-menu.open {
  display: block;
}

.mobile-menu-link {
  display: block;
  padding: 0.75rem;
  color: var(--text-primary);
  text-decoration: none;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.mobile-menu-link:hover,
.mobile-menu-link.active {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

.dark .mobile-menu-link:hover,
.dark .mobile-menu-link.active {
  background-color: rgba(59, 130, 246, 0.1);
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-bg {
  position: absolute;
  inset: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  padding: 0 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.search-form {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.search-field label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

.search-field input,
.search-field select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--gray-300);
  border-radius: 0.5rem;
  font-size: 0.875rem;
  color: var(--gray-900);
}

.search-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 2rem;
  background-color: var(--primary-600);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-button:hover {
  background-color: var(--primary-700);
}

/* Room Cards */
.room-card {
  background-color: var(--bg-primary);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.room-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.room-image {
  position: relative;
  height: 16rem;
  overflow: hidden;
}

.room-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.room-card:hover .room-image img {
  transform: scale(1.05);
}

.room-rating {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 9999px;
  padding: 0.25rem 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.room-content {
  padding: 1.5rem;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.room-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.room-description {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.room-price {
  text-align: right;
}

.room-price-amount {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-600);
}

.room-price-period {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.room-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  padding: 1rem;
  background-color: var(--gray-50);
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.dark .room-info {
  background-color: var(--gray-700);
}

.room-info-item {
  text-align: center;
}

.room-info-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--text-secondary);
  margin: 0 auto 0.25rem;
}

.room-info-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
}

.room-amenities {
  margin-bottom: 1.5rem;
}

.room-amenities-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.amenities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.amenity-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background-color: var(--primary-50);
  color: var(--primary-700);
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
}

.dark .amenity-tag {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary-400);
}

.amenity-more {
  background-color: var(--gray-100);
  color: var(--text-secondary);
}

.dark .amenity-more {
  background-color: var(--gray-700);
  color: var(--text-muted);
}

/* Footer */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-100);
  padding: 3rem 0;
}

.dark .footer {
  background-color: #000000;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
  color: var(--gray-300);
  font-size: 0.875rem;
  line-height: 1.6;
}

.footer-section a {
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--primary-400);
}

.footer-bottom {
  padding-top: 2rem;
  border-top: 1px solid var(--gray-800);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
}

/* Dashboard Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--bg-primary);
  padding: 1.5rem;
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-info h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.stat-info .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--green-600);
}

.stat-icon {
  padding: 0.75rem;
  border-radius: 0.75rem;
  color: white;
}

/* Table Styles */
.table-container {
  background-color: var(--bg-primary);
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.table-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.table-header h2 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table thead {
  background-color: var(--gray-50);
}

.dark .table thead {
  background-color: var(--gray-700);
}

.table th {
  padding: 0.75rem 1.5rem;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.875rem;
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

.dark .table tbody tr:hover {
  background-color: var(--gray-700);
}

.status-badge {
  display: inline-flex;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 9999px;
  text-transform: capitalize;
}

.status-confirmed {
  background-color: var(--green-100);
  color: var(--green-800);
}

.status-pending {
  background-color: var(--yellow-100);
  color: var(--yellow-800);
}

.status-cancelled {
  background-color: var(--red-100);
  color: var(--red-800);
}

.dark .status-confirmed {
  background-color: rgba(34, 197, 94, 0.1);
  color: var(--green-400);
}

.dark .status-pending {
  background-color: rgba(234, 179, 8, 0.1);
  color: var(--yellow-400);
}

.dark .status-cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--red-400);
}

/* Gallery Styles */
.gallery-filters {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 3rem;
}

.filter-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.filter-btn.active {
  background-color: var(--primary-600);
  color: white;
  box-shadow: var(--shadow-lg);
}

.filter-btn:not(.active) {
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  box-shadow: var(--shadow);
}

.filter-btn:not(.active):hover {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

.dark .filter-btn:not(.active):hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: var(--shadow-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  group: true;
}

.gallery-item:hover {
  box-shadow: var(--shadow-xl);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  background-color: rgba(0, 0, 0, 0.3);
}

.gallery-zoom-icon {
  width: 2rem;
  height: 2rem;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-zoom-icon {
  opacity: 1;
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 1rem;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-caption {
  opacity: 1;
}

/* Lightbox */
.lightbox {
  position: fixed;
  inset: 0;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.lightbox-close {
  position: absolute;
  top: -3rem;
  right: 0;
  color: white;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  transition: color 0.3s ease;
}

.lightbox-close:hover {
  color: var(--gray-300);
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: white;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.lightbox-nav:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

.lightbox-prev {
  left: 1rem;
}

.lightbox-next {
  right: 1rem;
}

.lightbox img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 0.5rem;
}

.lightbox-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 1.5rem;
  color: white;
}

.lightbox-title {
  font-size: 1.125rem;
  font-weight: 600;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .navbar-nav {
    display: none;
  }

  .navbar-actions .btn {
    display: none;
  }

  #mobile-menu-btn {
    display: block !important;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .search-form {
    padding: 1rem;
  }

  .search-grid {
    grid-template-columns: 1fr;
  }

  .room-header {
    flex-direction: column;
    gap: 1rem;
  }

  .room-price {
    text-align: left;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .footer-grid {
    grid-template-columns: 1fr;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .table-container {
    overflow-x: auto;
  }

  .table {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 1.5rem;
  }

  .text-4xl {
    font-size: 1.875rem;
  }

  .text-5xl {
    font-size: 2.25rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .room-info {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .amenities-list {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (min-width: 769px) {
  .mobile-menu {
    display: none !important;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:text-left {
    text-align: left;
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* Dark mode specific adjustments */
.dark {
  color-scheme: dark;
}

.dark body {
  background-color: var(--gray-900);
  color: var(--gray-100);
}

.dark .card {
  background-color: var(--gray-800);
  border-color: var(--gray-700);
}

.dark .form-input,
.dark .form-select,
.dark .form-textarea {
  background-color: var(--gray-700);
  border-color: var(--gray-600);
  color: var(--gray-100);
}

.dark .form-input:focus,
.dark .form-select:focus,
.dark .form-textarea:focus {
  border-color: var(--primary-400);
}

/* Utility classes for dark mode */
.dark .text-gray-900 { color: var(--gray-100); }
.dark .text-gray-800 { color: var(--gray-200); }
.dark .text-gray-700 { color: var(--gray-300); }
.dark .text-gray-600 { color: var(--gray-400); }
.dark .text-gray-500 { color: var(--gray-500); }
.dark .text-gray-400 { color: var(--gray-600); }
.dark .text-gray-300 { color: var(--gray-700); }

.dark .bg-white { background-color: var(--gray-800); }
.dark .bg-gray-50 { background-color: var(--gray-800); }
.dark .bg-gray-100 { background-color: var(--gray-700); }

.dark .border-gray-200 { border-color: var(--gray-700); }
.dark .border-gray-300 { border-color: var(--gray-600); }
