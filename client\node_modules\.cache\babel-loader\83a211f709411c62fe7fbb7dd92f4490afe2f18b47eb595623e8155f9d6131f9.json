{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v6\",\n  key: \"1holv5\"\n}], [\"path\", {\n  d: \"M16.76 3a2 2 0 0 1 1.8 1.1l2.23 4.479a2 2 0 0 1 .21.891V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9.472a2 2 0 0 1 .211-.894L5.45 4.1A2 2 0 0 1 7.24 3z\",\n  key: \"187q7i\"\n}], [\"path\", {\n  d: \"M3.054 9.013h17.893\",\n  key: \"grwhos\"\n}]];\nconst Package2 = createLucideIcon(\"package-2\", __iconNode);\nexport { __iconNode, Package2 as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Package2", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\package-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3v6', key: '1holv5' }],\n  [\n    'path',\n    {\n      d: 'M16.76 3a2 2 0 0 1 1.8 1.1l2.23 4.479a2 2 0 0 1 .21.891V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9.472a2 2 0 0 1 .211-.894L5.45 4.1A2 2 0 0 1 7.24 3z',\n      key: '187q7i',\n    },\n  ],\n  ['path', { d: 'M3.054 9.013h17.893', key: 'grwhos' }],\n];\n\n/**\n * @component @name Package2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3Y2IiAvPgogIDxwYXRoIGQ9Ik0xNi43NiAzYTIgMiAwIDAgMSAxLjggMS4xbDIuMjMgNC40NzlhMiAyIDAgMCAxIC4yMS44OTFWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOS40NzJhMiAyIDAgMCAxIC4yMTEtLjg5NEw1LjQ1IDQuMUEyIDIgMCAwIDEgNy4yNCAzeiIgLz4KICA8cGF0aCBkPSJNMy4wNTQgOS4wMTNoMTcuODkzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/package-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Package2 = createLucideIcon('package-2', __iconNode);\n\nexport default Package2;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAuBC,GAAA,EAAK;AAAA,CAAU,EACtD;AAaA,MAAMC,QAAA,GAAWC,gBAAA,CAAiB,aAAaJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}