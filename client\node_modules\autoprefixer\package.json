{"name": "autoprefixer", "version": "9.8.8", "description": "Parse CSS and add vendor prefixes to CSS rules using values from the Can I Use website", "keywords": ["autoprefixer", "css", "prefix", "postcss", "postcss-plugin"], "bin": "./bin/autoprefixer", "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "postcss/autoprefixer", "dependencies": {"browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001109", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "picocolors": "^0.2.1", "postcss": "^7.0.32", "postcss-value-parser": "^4.1.0"}, "main": "lib/autoprefixer"}