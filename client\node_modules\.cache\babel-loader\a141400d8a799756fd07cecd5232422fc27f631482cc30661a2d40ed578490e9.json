{"ast": null, "code": "function camelToDash(str) {\n  return str.replace(/([A-Z])/g, match => `-${match.toLowerCase()}`);\n}\nexport { camelToDash };", "map": {"version": 3, "names": ["camelToDash", "str", "replace", "match", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/render/dom/utils/camel-to-dash.mjs"], "sourcesContent": ["function camelToDash(str) {\n    return str.replace(/([A-Z])/g, (match) => `-${match.toLowerCase()}`);\n}\n\nexport { camelToDash };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,CAACC,OAAO,CAAC,UAAU,EAAGC,KAAK,IAAK,IAAIA,KAAK,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC;AACxE;AAEA,SAASJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}