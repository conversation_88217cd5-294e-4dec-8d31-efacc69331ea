{"ast": null, "code": "/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n  axis.min = originAxis.min;\n  axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n  copyAxisInto(box.x, originBox.x);\n  copyAxisInto(box.y, originBox.y);\n}\n/**\n * Reset a delta to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisDeltaInto(delta, originDelta) {\n  delta.translate = originDelta.translate;\n  delta.scale = originDelta.scale;\n  delta.originPoint = originDelta.originPoint;\n  delta.origin = originDelta.origin;\n}\nexport { copyAxisDeltaInto, copyAxisInto, copyBoxInto };", "map": {"version": 3, "names": ["copyAxisInto", "axis", "originAxis", "min", "max", "copyBoxInto", "box", "originBox", "x", "y", "copyAxisDeltaInto", "delta", "<PERSON><PERSON><PERSON><PERSON>", "translate", "scale", "originPoint", "origin"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs"], "sourcesContent": ["/**\n * Reset an axis to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisInto(axis, originAxis) {\n    axis.min = originAxis.min;\n    axis.max = originAxis.max;\n}\n/**\n * Reset a box to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyBoxInto(box, originBox) {\n    copyAxisInto(box.x, originBox.x);\n    copyAxisInto(box.y, originBox.y);\n}\n/**\n * Reset a delta to the provided origin box.\n *\n * This is a mutative operation.\n */\nfunction copyAxisDeltaInto(delta, originDelta) {\n    delta.translate = originDelta.translate;\n    delta.scale = originDelta.scale;\n    delta.originPoint = originDelta.originPoint;\n    delta.origin = originDelta.origin;\n}\n\nexport { copyAxisDeltaInto, copyAxisInto, copyBoxInto };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,IAAI,EAAEC,UAAU,EAAE;EACpCD,IAAI,CAACE,GAAG,GAAGD,UAAU,CAACC,GAAG;EACzBF,IAAI,CAACG,GAAG,GAAGF,UAAU,CAACE,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACjCP,YAAY,CAACM,GAAG,CAACE,CAAC,EAAED,SAAS,CAACC,CAAC,CAAC;EAChCR,YAAY,CAACM,GAAG,CAACG,CAAC,EAAEF,SAAS,CAACE,CAAC,CAAC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAC3CD,KAAK,CAACE,SAAS,GAAGD,WAAW,CAACC,SAAS;EACvCF,KAAK,CAACG,KAAK,GAAGF,WAAW,CAACE,KAAK;EAC/BH,KAAK,CAACI,WAAW,GAAGH,WAAW,CAACG,WAAW;EAC3CJ,KAAK,CAACK,MAAM,GAAGJ,WAAW,CAACI,MAAM;AACrC;AAEA,SAASN,iBAAiB,EAAEV,YAAY,EAAEK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}