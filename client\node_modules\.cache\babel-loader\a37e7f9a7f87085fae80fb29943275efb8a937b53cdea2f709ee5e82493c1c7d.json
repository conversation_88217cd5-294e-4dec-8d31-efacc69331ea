{"ast": null, "code": "import { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../context/SwitchLayoutGroupContext.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n  const {\n    visualElement: parent\n  } = useContext(MotionContext);\n  const lazyContext = useContext(LazyContext);\n  const presenceContext = useContext(PresenceContext);\n  const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n  const visualElementRef = useRef(null);\n  /**\n   * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n   */\n  createVisualElement = createVisualElement || lazyContext.renderer;\n  if (!visualElementRef.current && createVisualElement) {\n    visualElementRef.current = createVisualElement(Component, {\n      visualState,\n      parent,\n      props,\n      presenceContext,\n      blockInitialAnimation: presenceContext ? presenceContext.initial === false : false,\n      reducedMotionConfig\n    });\n  }\n  const visualElement = visualElementRef.current;\n  /**\n   * Load Motion gesture and animation features. These are rendered as renderless\n   * components so each feature can optionally make use of React lifecycle methods.\n   */\n  const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n  if (visualElement && !visualElement.projection && ProjectionNodeConstructor && (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n    createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n  }\n  const isMounted = useRef(false);\n  useInsertionEffect(() => {\n    /**\n     * Check the component has already mounted before calling\n     * `update` unnecessarily. This ensures we skip the initial update.\n     */\n    if (visualElement && isMounted.current) {\n      visualElement.update(props, presenceContext);\n    }\n  });\n  /**\n   * Cache this value as we want to know whether HandoffAppearAnimations\n   * was present on initial render - it will be deleted after this.\n   */\n  const optimisedAppearId = props[optimizedAppearDataAttribute];\n  const wantsHandoff = useRef(Boolean(optimisedAppearId) && !window.MotionHandoffIsComplete?.(optimisedAppearId) && window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n  useIsomorphicLayoutEffect(() => {\n    if (!visualElement) return;\n    isMounted.current = true;\n    window.MotionIsMounted = true;\n    visualElement.updateFeatures();\n    visualElement.scheduleRenderMicrotask();\n    /**\n     * Ideally this function would always run in a useEffect.\n     *\n     * However, if we have optimised appear animations to handoff from,\n     * it needs to happen synchronously to ensure there's no flash of\n     * incorrect styles in the event of a hydration error.\n     *\n     * So if we detect a situtation where optimised appear animations\n     * are running, we use useLayoutEffect to trigger animations.\n     */\n    if (wantsHandoff.current && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n  });\n  useEffect(() => {\n    if (!visualElement) return;\n    if (!wantsHandoff.current && visualElement.animationState) {\n      visualElement.animationState.animateChanges();\n    }\n    if (wantsHandoff.current) {\n      // This ensures all future calls to animateChanges() in this component will run in useEffect\n      queueMicrotask(() => {\n        window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n      });\n      wantsHandoff.current = false;\n    }\n    /**\n     * Now we've finished triggering animations for this element we\n     * can wipe the enteringChildren set for the next render.\n     */\n    visualElement.enteringChildren = undefined;\n  });\n  return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n  const {\n    layoutId,\n    layout,\n    drag,\n    dragConstraints,\n    layoutScroll,\n    layoutRoot,\n    layoutCrossfade\n  } = props;\n  visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"] ? undefined : getClosestProjectingNode(visualElement.parent));\n  visualElement.projection.setOptions({\n    layoutId,\n    layout,\n    alwaysMeasureLayout: Boolean(drag) || dragConstraints && isRefObject(dragConstraints),\n    visualElement,\n    /**\n     * TODO: Update options in an effect. This could be tricky as it'll be too late\n     * to update by the time layout animations run.\n     * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n     * ensuring it gets called if there's no potential layout animations.\n     *\n     */\n    animationType: typeof layout === \"string\" ? layout : \"both\",\n    initialPromotionConfig,\n    crossfade: layoutCrossfade,\n    layoutScroll,\n    layoutRoot\n  });\n}\nfunction getClosestProjectingNode(visualElement) {\n  if (!visualElement) return undefined;\n  return visualElement.options.allowProjection !== false ? visualElement.projection : getClosestProjectingNode(visualElement.parent);\n}\nexport { useVisualElement };", "map": {"version": 3, "names": ["useContext", "useRef", "useInsertionEffect", "useEffect", "optimizedAppearDataAttribute", "LazyContext", "MotionConfigContext", "MotionContext", "PresenceContext", "SwitchLayoutGroupContext", "isRefObject", "useIsomorphicLayoutEffect", "useVisualElement", "Component", "visualState", "props", "createVisualElement", "ProjectionNodeConstructor", "visualElement", "parent", "lazyContext", "presenceContext", "reducedMotionConfig", "reducedMotion", "visualElementRef", "renderer", "current", "blockInitialAnimation", "initial", "initialLayoutGroupConfig", "projection", "type", "createProjectionNode", "isMounted", "update", "optimisedAppearId", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "window", "MotionHandoffIsComplete", "MotionHasOptimisedAnimation", "MotionIsMounted", "updateFeatures", "scheduleRenderMicrotask", "animationState", "animateChanges", "queueMicrotask", "MotionHandoffMarkAsComplete", "enteringC<PERSON><PERSON>n", "undefined", "initialPromotionConfig", "layoutId", "layout", "drag", "dragConstraints", "layoutScroll", "layoutRoot", "layoutCrossfade", "latestValues", "getClosestProjectingNode", "setOptions", "alwaysMeasureLayout", "animationType", "crossfade", "options", "allowProjection"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs"], "sourcesContent": ["import { useContext, useRef, useInsertionEffect, useEffect } from 'react';\nimport { optimizedAppearDataAttribute } from '../../animation/optimized-appear/data-id.mjs';\nimport { LazyContext } from '../../context/LazyContext.mjs';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\nimport { MotionContext } from '../../context/MotionContext/index.mjs';\nimport { PresenceContext } from '../../context/PresenceContext.mjs';\nimport { SwitchLayoutGroupContext } from '../../context/SwitchLayoutGroupContext.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { useIsomorphicLayoutEffect } from '../../utils/use-isomorphic-effect.mjs';\n\nfunction useVisualElement(Component, visualState, props, createVisualElement, ProjectionNodeConstructor) {\n    const { visualElement: parent } = useContext(MotionContext);\n    const lazyContext = useContext(LazyContext);\n    const presenceContext = useContext(PresenceContext);\n    const reducedMotionConfig = useContext(MotionConfigContext).reducedMotion;\n    const visualElementRef = useRef(null);\n    /**\n     * If we haven't preloaded a renderer, check to see if we have one lazy-loaded\n     */\n    createVisualElement =\n        createVisualElement ||\n            lazyContext.renderer;\n    if (!visualElementRef.current && createVisualElement) {\n        visualElementRef.current = createVisualElement(Component, {\n            visualState,\n            parent,\n            props,\n            presenceContext,\n            blockInitialAnimation: presenceContext\n                ? presenceContext.initial === false\n                : false,\n            reducedMotionConfig,\n        });\n    }\n    const visualElement = visualElementRef.current;\n    /**\n     * Load Motion gesture and animation features. These are rendered as renderless\n     * components so each feature can optionally make use of React lifecycle methods.\n     */\n    const initialLayoutGroupConfig = useContext(SwitchLayoutGroupContext);\n    if (visualElement &&\n        !visualElement.projection &&\n        ProjectionNodeConstructor &&\n        (visualElement.type === \"html\" || visualElement.type === \"svg\")) {\n        createProjectionNode(visualElementRef.current, props, ProjectionNodeConstructor, initialLayoutGroupConfig);\n    }\n    const isMounted = useRef(false);\n    useInsertionEffect(() => {\n        /**\n         * Check the component has already mounted before calling\n         * `update` unnecessarily. This ensures we skip the initial update.\n         */\n        if (visualElement && isMounted.current) {\n            visualElement.update(props, presenceContext);\n        }\n    });\n    /**\n     * Cache this value as we want to know whether HandoffAppearAnimations\n     * was present on initial render - it will be deleted after this.\n     */\n    const optimisedAppearId = props[optimizedAppearDataAttribute];\n    const wantsHandoff = useRef(Boolean(optimisedAppearId) &&\n        !window.MotionHandoffIsComplete?.(optimisedAppearId) &&\n        window.MotionHasOptimisedAnimation?.(optimisedAppearId));\n    useIsomorphicLayoutEffect(() => {\n        if (!visualElement)\n            return;\n        isMounted.current = true;\n        window.MotionIsMounted = true;\n        visualElement.updateFeatures();\n        visualElement.scheduleRenderMicrotask();\n        /**\n         * Ideally this function would always run in a useEffect.\n         *\n         * However, if we have optimised appear animations to handoff from,\n         * it needs to happen synchronously to ensure there's no flash of\n         * incorrect styles in the event of a hydration error.\n         *\n         * So if we detect a situtation where optimised appear animations\n         * are running, we use useLayoutEffect to trigger animations.\n         */\n        if (wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n    });\n    useEffect(() => {\n        if (!visualElement)\n            return;\n        if (!wantsHandoff.current && visualElement.animationState) {\n            visualElement.animationState.animateChanges();\n        }\n        if (wantsHandoff.current) {\n            // This ensures all future calls to animateChanges() in this component will run in useEffect\n            queueMicrotask(() => {\n                window.MotionHandoffMarkAsComplete?.(optimisedAppearId);\n            });\n            wantsHandoff.current = false;\n        }\n        /**\n         * Now we've finished triggering animations for this element we\n         * can wipe the enteringChildren set for the next render.\n         */\n        visualElement.enteringChildren = undefined;\n    });\n    return visualElement;\n}\nfunction createProjectionNode(visualElement, props, ProjectionNodeConstructor, initialPromotionConfig) {\n    const { layoutId, layout, drag, dragConstraints, layoutScroll, layoutRoot, layoutCrossfade, } = props;\n    visualElement.projection = new ProjectionNodeConstructor(visualElement.latestValues, props[\"data-framer-portal-id\"]\n        ? undefined\n        : getClosestProjectingNode(visualElement.parent));\n    visualElement.projection.setOptions({\n        layoutId,\n        layout,\n        alwaysMeasureLayout: Boolean(drag) || (dragConstraints && isRefObject(dragConstraints)),\n        visualElement,\n        /**\n         * TODO: Update options in an effect. This could be tricky as it'll be too late\n         * to update by the time layout animations run.\n         * We also need to fix this safeToRemove by linking it up to the one returned by usePresence,\n         * ensuring it gets called if there's no potential layout animations.\n         *\n         */\n        animationType: typeof layout === \"string\" ? layout : \"both\",\n        initialPromotionConfig,\n        crossfade: layoutCrossfade,\n        layoutScroll,\n        layoutRoot,\n    });\n}\nfunction getClosestProjectingNode(visualElement) {\n    if (!visualElement)\n        return undefined;\n    return visualElement.options.allowProjection !== false\n        ? visualElement.projection\n        : getClosestProjectingNode(visualElement.parent);\n}\n\nexport { useVisualElement };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,OAAO;AACzE,SAASC,4BAA4B,QAAQ,8CAA8C;AAC3F,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,mBAAmB,QAAQ,uCAAuC;AAC3E,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,wBAAwB,QAAQ,4CAA4C;AACrF,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,yBAAyB,QAAQ,uCAAuC;AAEjF,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAE;EACrG,MAAM;IAAEC,aAAa,EAAEC;EAAO,CAAC,GAAGnB,UAAU,CAACO,aAAa,CAAC;EAC3D,MAAMa,WAAW,GAAGpB,UAAU,CAACK,WAAW,CAAC;EAC3C,MAAMgB,eAAe,GAAGrB,UAAU,CAACQ,eAAe,CAAC;EACnD,MAAMc,mBAAmB,GAAGtB,UAAU,CAACM,mBAAmB,CAAC,CAACiB,aAAa;EACzE,MAAMC,gBAAgB,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACrC;AACJ;AACA;EACIe,mBAAmB,GACfA,mBAAmB,IACfI,WAAW,CAACK,QAAQ;EAC5B,IAAI,CAACD,gBAAgB,CAACE,OAAO,IAAIV,mBAAmB,EAAE;IAClDQ,gBAAgB,CAACE,OAAO,GAAGV,mBAAmB,CAACH,SAAS,EAAE;MACtDC,WAAW;MACXK,MAAM;MACNJ,KAAK;MACLM,eAAe;MACfM,qBAAqB,EAAEN,eAAe,GAChCA,eAAe,CAACO,OAAO,KAAK,KAAK,GACjC,KAAK;MACXN;IACJ,CAAC,CAAC;EACN;EACA,MAAMJ,aAAa,GAAGM,gBAAgB,CAACE,OAAO;EAC9C;AACJ;AACA;AACA;EACI,MAAMG,wBAAwB,GAAG7B,UAAU,CAACS,wBAAwB,CAAC;EACrE,IAAIS,aAAa,IACb,CAACA,aAAa,CAACY,UAAU,IACzBb,yBAAyB,KACxBC,aAAa,CAACa,IAAI,KAAK,MAAM,IAAIb,aAAa,CAACa,IAAI,KAAK,KAAK,CAAC,EAAE;IACjEC,oBAAoB,CAACR,gBAAgB,CAACE,OAAO,EAAEX,KAAK,EAAEE,yBAAyB,EAAEY,wBAAwB,CAAC;EAC9G;EACA,MAAMI,SAAS,GAAGhC,MAAM,CAAC,KAAK,CAAC;EAC/BC,kBAAkB,CAAC,MAAM;IACrB;AACR;AACA;AACA;IACQ,IAAIgB,aAAa,IAAIe,SAAS,CAACP,OAAO,EAAE;MACpCR,aAAa,CAACgB,MAAM,CAACnB,KAAK,EAAEM,eAAe,CAAC;IAChD;EACJ,CAAC,CAAC;EACF;AACJ;AACA;AACA;EACI,MAAMc,iBAAiB,GAAGpB,KAAK,CAACX,4BAA4B,CAAC;EAC7D,MAAMgC,YAAY,GAAGnC,MAAM,CAACoC,OAAO,CAACF,iBAAiB,CAAC,IAClD,CAACG,MAAM,CAACC,uBAAuB,GAAGJ,iBAAiB,CAAC,IACpDG,MAAM,CAACE,2BAA2B,GAAGL,iBAAiB,CAAC,CAAC;EAC5DxB,yBAAyB,CAAC,MAAM;IAC5B,IAAI,CAACO,aAAa,EACd;IACJe,SAAS,CAACP,OAAO,GAAG,IAAI;IACxBY,MAAM,CAACG,eAAe,GAAG,IAAI;IAC7BvB,aAAa,CAACwB,cAAc,CAAC,CAAC;IAC9BxB,aAAa,CAACyB,uBAAuB,CAAC,CAAC;IACvC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIP,YAAY,CAACV,OAAO,IAAIR,aAAa,CAAC0B,cAAc,EAAE;MACtD1B,aAAa,CAAC0B,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;EACJ,CAAC,CAAC;EACF1C,SAAS,CAAC,MAAM;IACZ,IAAI,CAACe,aAAa,EACd;IACJ,IAAI,CAACkB,YAAY,CAACV,OAAO,IAAIR,aAAa,CAAC0B,cAAc,EAAE;MACvD1B,aAAa,CAAC0B,cAAc,CAACC,cAAc,CAAC,CAAC;IACjD;IACA,IAAIT,YAAY,CAACV,OAAO,EAAE;MACtB;MACAoB,cAAc,CAAC,MAAM;QACjBR,MAAM,CAACS,2BAA2B,GAAGZ,iBAAiB,CAAC;MAC3D,CAAC,CAAC;MACFC,YAAY,CAACV,OAAO,GAAG,KAAK;IAChC;IACA;AACR;AACA;AACA;IACQR,aAAa,CAAC8B,gBAAgB,GAAGC,SAAS;EAC9C,CAAC,CAAC;EACF,OAAO/B,aAAa;AACxB;AACA,SAASc,oBAAoBA,CAACd,aAAa,EAAEH,KAAK,EAAEE,yBAAyB,EAAEiC,sBAAsB,EAAE;EACnG,MAAM;IAAEC,QAAQ;IAAEC,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAiB,CAAC,GAAG1C,KAAK;EACrGG,aAAa,CAACY,UAAU,GAAG,IAAIb,yBAAyB,CAACC,aAAa,CAACwC,YAAY,EAAE3C,KAAK,CAAC,uBAAuB,CAAC,GAC7GkC,SAAS,GACTU,wBAAwB,CAACzC,aAAa,CAACC,MAAM,CAAC,CAAC;EACrDD,aAAa,CAACY,UAAU,CAAC8B,UAAU,CAAC;IAChCT,QAAQ;IACRC,MAAM;IACNS,mBAAmB,EAAExB,OAAO,CAACgB,IAAI,CAAC,IAAKC,eAAe,IAAI5C,WAAW,CAAC4C,eAAe,CAAE;IACvFpC,aAAa;IACb;AACR;AACA;AACA;AACA;AACA;AACA;IACQ4C,aAAa,EAAE,OAAOV,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,MAAM;IAC3DF,sBAAsB;IACtBa,SAAS,EAAEN,eAAe;IAC1BF,YAAY;IACZC;EACJ,CAAC,CAAC;AACN;AACA,SAASG,wBAAwBA,CAACzC,aAAa,EAAE;EAC7C,IAAI,CAACA,aAAa,EACd,OAAO+B,SAAS;EACpB,OAAO/B,aAAa,CAAC8C,OAAO,CAACC,eAAe,KAAK,KAAK,GAChD/C,aAAa,CAACY,UAAU,GACxB6B,wBAAwB,CAACzC,aAAa,CAACC,MAAM,CAAC;AACxD;AAEA,SAASP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}