{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hotel mangement\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { BookingProvider } from './contexts/BookingContext';\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport Home from './pages/Home';\nimport Rooms from './pages/Rooms';\nimport Facilities from './pages/Facilities';\nimport Gallery from './pages/Gallery';\nimport Contact from './pages/Contact';\nimport Dashboard from './pages/Dashboard';\nimport MyBookings from './pages/MyBookings';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(BookingProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"min-h-screen\",\n          style: {\n            backgroundColor: 'var(--bg-primary)',\n            color: 'var(--text-primary)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n            children: /*#__PURE__*/_jsxDEV(Routes, {\n              children: [/*#__PURE__*/_jsxDEV(Route, {\n                path: \"/\",\n                element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 24,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/rooms\",\n                element: /*#__PURE__*/_jsxDEV(Rooms, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 25,\n                  columnNumber: 47\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/facilities\",\n                element: /*#__PURE__*/_jsxDEV(Facilities, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 26,\n                  columnNumber: 52\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/gallery\",\n                element: /*#__PURE__*/_jsxDEV(Gallery, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/contact\",\n                element: /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 28,\n                  columnNumber: 49\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/dashboard\",\n                element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 29,\n                  columnNumber: 51\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                path: \"/my-bookings\",\n                element: /*#__PURE__*/_jsxDEV(MyBookings, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 30,\n                  columnNumber: 53\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ThemeProvider", "BookingProvider", "<PERSON><PERSON><PERSON>", "Footer", "Home", "Rooms", "Facilities", "Gallery", "Contact", "Dashboard", "MyBookings", "jsxDEV", "_jsxDEV", "App", "children", "className", "style", "backgroundColor", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport { BookingProvider } from './contexts/BookingContext';\nimport Navbar from './components/Navbar';\nimport Footer from './components/Footer';\nimport Home from './pages/Home';\nimport Rooms from './pages/Rooms';\nimport Facilities from './pages/Facilities';\nimport Gallery from './pages/Gallery';\nimport Contact from './pages/Contact';\nimport Dashboard from './pages/Dashboard';\nimport MyBookings from './pages/MyBookings';\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <BookingProvider>\n        <Router>\n          <div className=\"min-h-screen\" style={{ backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }}>\n            <Navbar />\n            <main>\n              <Routes>\n                <Route path=\"/\" element={<Home />} />\n                <Route path=\"/rooms\" element={<Rooms />} />\n                <Route path=\"/facilities\" element={<Facilities />} />\n                <Route path=\"/gallery\" element={<Gallery />} />\n                <Route path=\"/contact\" element={<Contact />} />\n                <Route path=\"/dashboard\" element={<Dashboard />} />\n                <Route path=\"/my-bookings\" element={<MyBookings />} />\n              </Routes>\n            </main>\n            <Footer />\n          </div>\n        </Router>\n      </BookingProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACZ,aAAa;IAAAc,QAAA,eACZF,OAAA,CAACX,eAAe;MAAAa,QAAA,eACdF,OAAA,CAACf,MAAM;QAAAiB,QAAA,eACLF,OAAA;UAAKG,SAAS,EAAC,cAAc;UAACC,KAAK,EAAE;YAAEC,eAAe,EAAE,mBAAmB;YAAEC,KAAK,EAAE;UAAsB,CAAE;UAAAJ,QAAA,gBAC1GF,OAAA,CAACV,MAAM;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACVV,OAAA;YAAAE,QAAA,eACEF,OAAA,CAACd,MAAM;cAAAgB,QAAA,gBACLF,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,GAAG;gBAACC,OAAO,eAAEZ,OAAA,CAACR,IAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrCV,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,QAAQ;gBAACC,OAAO,eAAEZ,OAAA,CAACP,KAAK;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CV,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,aAAa;gBAACC,OAAO,eAAEZ,OAAA,CAACN,UAAU;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrDV,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEZ,OAAA,CAACL,OAAO;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CV,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,UAAU;gBAACC,OAAO,eAAEZ,OAAA,CAACJ,OAAO;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/CV,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,YAAY;gBAACC,OAAO,eAAEZ,OAAA,CAACH,SAAS;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDV,OAAA,CAACb,KAAK;gBAACwB,IAAI,EAAC,cAAc;gBAACC,OAAO,eAAEZ,OAAA,CAACF,UAAU;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACPV,OAAA,CAACT,MAAM;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEpB;AAACG,EAAA,GAxBQZ,GAAG;AA0BZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}