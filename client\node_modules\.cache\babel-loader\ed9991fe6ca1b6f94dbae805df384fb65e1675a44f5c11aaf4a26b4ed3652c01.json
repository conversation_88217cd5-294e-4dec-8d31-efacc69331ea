{"ast": null, "code": "import { isSVGElement } from '../utils/is-svg-element.mjs';\nimport { resolveElements } from '../utils/resolve-elements.mjs';\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n  if (borderBoxSize && borderBoxSize[0]) {\n    return borderBoxSize[0][borderBoxAxis + \"Size\"];\n  } else if (isSVGElement(target) && \"getBBox\" in target) {\n    return target.getBBox()[svgAxis];\n  } else {\n    return target[htmlAxis];\n  }\n};\nconst getWidth = /*@__PURE__*/getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget({\n  target,\n  borderBoxSize\n}) {\n  resizeHandlers.get(target)?.forEach(handler => {\n    handler(target, {\n      get width() {\n        return getWidth(target, borderBoxSize);\n      },\n      get height() {\n        return getHeight(target, borderBoxSize);\n      }\n    });\n  });\n}\nfunction notifyAll(entries) {\n  entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n  if (typeof ResizeObserver === \"undefined\") return;\n  observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n  if (!observer) createResizeObserver();\n  const elements = resolveElements(target);\n  elements.forEach(element => {\n    let elementHandlers = resizeHandlers.get(element);\n    if (!elementHandlers) {\n      elementHandlers = new Set();\n      resizeHandlers.set(element, elementHandlers);\n    }\n    elementHandlers.add(handler);\n    observer?.observe(element);\n  });\n  return () => {\n    elements.forEach(element => {\n      const elementHandlers = resizeHandlers.get(element);\n      elementHandlers?.delete(handler);\n      if (!elementHandlers?.size) {\n        observer?.unobserve(element);\n      }\n    });\n  };\n}\nexport { resizeElement };", "map": {"version": 3, "names": ["isSVGElement", "resolveElements", "resizeHandlers", "WeakMap", "observer", "getSize", "borderBoxAxis", "svgAxis", "htmlAxis", "target", "borderBoxSize", "getBBox", "getWidth", "getHeight", "notify<PERSON><PERSON><PERSON>", "get", "for<PERSON>ach", "handler", "width", "height", "notifyAll", "entries", "createResizeObserver", "ResizeObserver", "resizeElement", "elements", "element", "elementHandlers", "Set", "set", "add", "observe", "delete", "size", "unobserve"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/resize/handle-element.mjs"], "sourcesContent": ["import { isSVGElement } from '../utils/is-svg-element.mjs';\nimport { resolveElements } from '../utils/resolve-elements.mjs';\n\nconst resizeHandlers = new WeakMap();\nlet observer;\nconst getSize = (borderBoxAxis, svgAxis, htmlAxis) => (target, borderBoxSize) => {\n    if (borderBoxSize && borderBoxSize[0]) {\n        return borderBoxSize[0][(borderBoxAxis + \"Size\")];\n    }\n    else if (isSVGElement(target) && \"getBBox\" in target) {\n        return target.getBBox()[svgAxis];\n    }\n    else {\n        return target[htmlAxis];\n    }\n};\nconst getWidth = /*@__PURE__*/ getSize(\"inline\", \"width\", \"offsetWidth\");\nconst getHeight = /*@__PURE__*/ getSize(\"block\", \"height\", \"offsetHeight\");\nfunction notifyTarget({ target, borderBoxSize }) {\n    resizeHandlers.get(target)?.forEach((handler) => {\n        handler(target, {\n            get width() {\n                return getWidth(target, borderBoxSize);\n            },\n            get height() {\n                return getHeight(target, borderBoxSize);\n            },\n        });\n    });\n}\nfunction notifyAll(entries) {\n    entries.forEach(notifyTarget);\n}\nfunction createResizeObserver() {\n    if (typeof ResizeObserver === \"undefined\")\n        return;\n    observer = new ResizeObserver(notifyAll);\n}\nfunction resizeElement(target, handler) {\n    if (!observer)\n        createResizeObserver();\n    const elements = resolveElements(target);\n    elements.forEach((element) => {\n        let elementHandlers = resizeHandlers.get(element);\n        if (!elementHandlers) {\n            elementHandlers = new Set();\n            resizeHandlers.set(element, elementHandlers);\n        }\n        elementHandlers.add(handler);\n        observer?.observe(element);\n    });\n    return () => {\n        elements.forEach((element) => {\n            const elementHandlers = resizeHandlers.get(element);\n            elementHandlers?.delete(handler);\n            if (!elementHandlers?.size) {\n                observer?.unobserve(element);\n            }\n        });\n    };\n}\n\nexport { resizeElement };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,eAAe,QAAQ,+BAA+B;AAE/D,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AACpC,IAAIC,QAAQ;AACZ,MAAMC,OAAO,GAAGA,CAACC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,KAAK,CAACC,MAAM,EAAEC,aAAa,KAAK;EAC7E,IAAIA,aAAa,IAAIA,aAAa,CAAC,CAAC,CAAC,EAAE;IACnC,OAAOA,aAAa,CAAC,CAAC,CAAC,CAAEJ,aAAa,GAAG,MAAM,CAAE;EACrD,CAAC,MACI,IAAIN,YAAY,CAACS,MAAM,CAAC,IAAI,SAAS,IAAIA,MAAM,EAAE;IAClD,OAAOA,MAAM,CAACE,OAAO,CAAC,CAAC,CAACJ,OAAO,CAAC;EACpC,CAAC,MACI;IACD,OAAOE,MAAM,CAACD,QAAQ,CAAC;EAC3B;AACJ,CAAC;AACD,MAAMI,QAAQ,GAAG,aAAcP,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC;AACxE,MAAMQ,SAAS,GAAG,aAAcR,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,CAAC;AAC1E,SAASS,YAAYA,CAAC;EAAEL,MAAM;EAAEC;AAAc,CAAC,EAAE;EAC7CR,cAAc,CAACa,GAAG,CAACN,MAAM,CAAC,EAAEO,OAAO,CAAEC,OAAO,IAAK;IAC7CA,OAAO,CAACR,MAAM,EAAE;MACZ,IAAIS,KAAKA,CAAA,EAAG;QACR,OAAON,QAAQ,CAACH,MAAM,EAAEC,aAAa,CAAC;MAC1C,CAAC;MACD,IAAIS,MAAMA,CAAA,EAAG;QACT,OAAON,SAAS,CAACJ,MAAM,EAAEC,aAAa,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA,SAASU,SAASA,CAACC,OAAO,EAAE;EACxBA,OAAO,CAACL,OAAO,CAACF,YAAY,CAAC;AACjC;AACA,SAASQ,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,cAAc,KAAK,WAAW,EACrC;EACJnB,QAAQ,GAAG,IAAImB,cAAc,CAACH,SAAS,CAAC;AAC5C;AACA,SAASI,aAAaA,CAACf,MAAM,EAAEQ,OAAO,EAAE;EACpC,IAAI,CAACb,QAAQ,EACTkB,oBAAoB,CAAC,CAAC;EAC1B,MAAMG,QAAQ,GAAGxB,eAAe,CAACQ,MAAM,CAAC;EACxCgB,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;IAC1B,IAAIC,eAAe,GAAGzB,cAAc,CAACa,GAAG,CAACW,OAAO,CAAC;IACjD,IAAI,CAACC,eAAe,EAAE;MAClBA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC3B1B,cAAc,CAAC2B,GAAG,CAACH,OAAO,EAAEC,eAAe,CAAC;IAChD;IACAA,eAAe,CAACG,GAAG,CAACb,OAAO,CAAC;IAC5Bb,QAAQ,EAAE2B,OAAO,CAACL,OAAO,CAAC;EAC9B,CAAC,CAAC;EACF,OAAO,MAAM;IACTD,QAAQ,CAACT,OAAO,CAAEU,OAAO,IAAK;MAC1B,MAAMC,eAAe,GAAGzB,cAAc,CAACa,GAAG,CAACW,OAAO,CAAC;MACnDC,eAAe,EAAEK,MAAM,CAACf,OAAO,CAAC;MAChC,IAAI,CAACU,eAAe,EAAEM,IAAI,EAAE;QACxB7B,QAAQ,EAAE8B,SAAS,CAACR,OAAO,CAAC;MAChC;IACJ,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}