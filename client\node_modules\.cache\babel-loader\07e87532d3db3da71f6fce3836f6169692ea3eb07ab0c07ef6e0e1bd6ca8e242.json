{"ast": null, "code": "import { MotionValue } from '../../value/index.mjs';\nimport { px } from '../../value/types/numbers/units.mjs';\nimport { addAttrValue } from '../attr/index.mjs';\nimport { addStyleValue } from '../style/index.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst toPx = px.transform;\nfunction addSVGPathValue(element, state, key, value) {\n  frame.render(() => element.setAttribute(\"pathLength\", \"1\"));\n  if (key === \"pathOffset\") {\n    return state.set(key, value, () => element.setAttribute(\"stroke-dashoffset\", toPx(-state.latest[key])));\n  } else {\n    if (!state.get(\"stroke-dasharray\")) {\n      state.set(\"stroke-dasharray\", new MotionValue(\"1 1\"), () => {\n        const {\n          pathLength = 1,\n          pathSpacing\n        } = state.latest;\n        element.setAttribute(\"stroke-dasharray\", `${toPx(pathLength)} ${toPx(pathSpacing ?? 1 - Number(pathLength))}`);\n      });\n    }\n    return state.set(key, value, undefined, state.get(\"stroke-dasharray\"));\n  }\n}\nconst addSVGValue = (element, state, key, value) => {\n  if (key.startsWith(\"path\")) {\n    return addSVGPathValue(element, state, key, value);\n  } else if (key.startsWith(\"attr\")) {\n    return addAttrValue(element, state, convertAttrKey(key), value);\n  }\n  const handler = key in element.style ? addStyleValue : addAttrValue;\n  return handler(element, state, key, value);\n};\nconst svgEffect = /*@__PURE__*/createSelectorEffect(/*@__PURE__*/createEffect(addSVGValue));\nfunction convertAttrKey(key) {\n  return key.replace(/^attr([A-Z])/, (_, firstChar) => firstChar.toLowerCase());\n}\nexport { svgEffect };", "map": {"version": 3, "names": ["MotionValue", "px", "addAttrValue", "addStyleValue", "createSelectorEffect", "createEffect", "frame", "toPx", "transform", "addSVGPathValue", "element", "state", "key", "value", "render", "setAttribute", "set", "latest", "get", "<PERSON><PERSON><PERSON><PERSON>", "pathSpacing", "Number", "undefined", "addSVGValue", "startsWith", "convertAttrKey", "handler", "style", "svgEffect", "replace", "_", "firstChar", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/effects/svg/index.mjs"], "sourcesContent": ["import { MotionValue } from '../../value/index.mjs';\nimport { px } from '../../value/types/numbers/units.mjs';\nimport { addAttrValue } from '../attr/index.mjs';\nimport { addStyleValue } from '../style/index.mjs';\nimport { createSelectorEffect } from '../utils/create-dom-effect.mjs';\nimport { createEffect } from '../utils/create-effect.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toPx = px.transform;\nfunction addSVGPathValue(element, state, key, value) {\n    frame.render(() => element.setAttribute(\"pathLength\", \"1\"));\n    if (key === \"pathOffset\") {\n        return state.set(key, value, () => element.setAttribute(\"stroke-dashoffset\", toPx(-state.latest[key])));\n    }\n    else {\n        if (!state.get(\"stroke-dasharray\")) {\n            state.set(\"stroke-dasharray\", new MotionValue(\"1 1\"), () => {\n                const { pathLength = 1, pathSpacing } = state.latest;\n                element.setAttribute(\"stroke-dasharray\", `${toPx(pathLength)} ${toPx(pathSpacing ?? 1 - Number(pathLength))}`);\n            });\n        }\n        return state.set(key, value, undefined, state.get(\"stroke-dasharray\"));\n    }\n}\nconst addSVGValue = (element, state, key, value) => {\n    if (key.startsWith(\"path\")) {\n        return addSVGPathValue(element, state, key, value);\n    }\n    else if (key.startsWith(\"attr\")) {\n        return addAttrValue(element, state, convertAttrKey(key), value);\n    }\n    const handler = key in element.style ? addStyleValue : addAttrValue;\n    return handler(element, state, key, value);\n};\nconst svgEffect = /*@__PURE__*/ createSelectorEffect(\n/*@__PURE__*/ createEffect(addSVGValue));\nfunction convertAttrKey(key) {\n    return key.replace(/^attr([A-Z])/, (_, firstChar) => firstChar.toLowerCase());\n}\n\nexport { svgEffect };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,uBAAuB;AACnD,SAASC,EAAE,QAAQ,qCAAqC;AACxD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,IAAI,GAAGN,EAAE,CAACO,SAAS;AACzB,SAASC,eAAeA,CAACC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACjDP,KAAK,CAACQ,MAAM,CAAC,MAAMJ,OAAO,CAACK,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;EAC3D,IAAIH,GAAG,KAAK,YAAY,EAAE;IACtB,OAAOD,KAAK,CAACK,GAAG,CAACJ,GAAG,EAAEC,KAAK,EAAE,MAAMH,OAAO,CAACK,YAAY,CAAC,mBAAmB,EAAER,IAAI,CAAC,CAACI,KAAK,CAACM,MAAM,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3G,CAAC,MACI;IACD,IAAI,CAACD,KAAK,CAACO,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAChCP,KAAK,CAACK,GAAG,CAAC,kBAAkB,EAAE,IAAIhB,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM;QACxD,MAAM;UAAEmB,UAAU,GAAG,CAAC;UAAEC;QAAY,CAAC,GAAGT,KAAK,CAACM,MAAM;QACpDP,OAAO,CAACK,YAAY,CAAC,kBAAkB,EAAE,GAAGR,IAAI,CAACY,UAAU,CAAC,IAAIZ,IAAI,CAACa,WAAW,IAAI,CAAC,GAAGC,MAAM,CAACF,UAAU,CAAC,CAAC,EAAE,CAAC;MAClH,CAAC,CAAC;IACN;IACA,OAAOR,KAAK,CAACK,GAAG,CAACJ,GAAG,EAAEC,KAAK,EAAES,SAAS,EAAEX,KAAK,CAACO,GAAG,CAAC,kBAAkB,CAAC,CAAC;EAC1E;AACJ;AACA,MAAMK,WAAW,GAAGA,CAACb,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,KAAK;EAChD,IAAID,GAAG,CAACY,UAAU,CAAC,MAAM,CAAC,EAAE;IACxB,OAAOf,eAAe,CAACC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,CAAC;EACtD,CAAC,MACI,IAAID,GAAG,CAACY,UAAU,CAAC,MAAM,CAAC,EAAE;IAC7B,OAAOtB,YAAY,CAACQ,OAAO,EAAEC,KAAK,EAAEc,cAAc,CAACb,GAAG,CAAC,EAAEC,KAAK,CAAC;EACnE;EACA,MAAMa,OAAO,GAAGd,GAAG,IAAIF,OAAO,CAACiB,KAAK,GAAGxB,aAAa,GAAGD,YAAY;EACnE,OAAOwB,OAAO,CAAChB,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,CAAC;AAC9C,CAAC;AACD,MAAMe,SAAS,GAAG,aAAcxB,oBAAoB,CACpD,aAAcC,YAAY,CAACkB,WAAW,CAAC,CAAC;AACxC,SAASE,cAAcA,CAACb,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACiB,OAAO,CAAC,cAAc,EAAE,CAACC,CAAC,EAAEC,SAAS,KAAKA,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC;AACjF;AAEA,SAASJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}