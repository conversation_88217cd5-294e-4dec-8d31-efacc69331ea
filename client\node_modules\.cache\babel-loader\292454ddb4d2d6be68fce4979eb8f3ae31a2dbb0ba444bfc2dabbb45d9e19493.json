{"ast": null, "code": "function getViewAnimationLayerInfo(pseudoElement) {\n  const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n  if (!match) return null;\n  return {\n    layer: match[2],\n    type: match[1]\n  };\n}\nexport { getViewAnimationLayerInfo };", "map": {"version": 3, "names": ["getViewAnimationLayerInfo", "pseudoElement", "match", "layer", "type"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/view/utils/get-layer-info.mjs"], "sourcesContent": ["function getViewAnimationLayerInfo(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\nexport { getViewAnimationLayerInfo };\n"], "mappings": "AAAA,SAASA,yBAAyBA,CAACC,aAAa,EAAE;EAC9C,MAAMC,KAAK,GAAGD,aAAa,CAACC,KAAK,CAAC,uDAAuD,CAAC;EAC1F,IAAI,CAACA,KAAK,EACN,OAAO,IAAI;EACf,OAAO;IAAEC,KAAK,EAAED,KAAK,CAAC,CAAC,CAAC;IAAEE,IAAI,EAAEF,KAAK,CAAC,CAAC;EAAE,CAAC;AAC9C;AAEA,SAASF,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}