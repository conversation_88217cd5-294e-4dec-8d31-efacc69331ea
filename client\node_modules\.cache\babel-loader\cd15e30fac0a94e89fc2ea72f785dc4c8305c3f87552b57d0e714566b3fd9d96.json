{"ast": null, "code": "const isDragging = {\n  x: false,\n  y: false\n};\nfunction isDragActive() {\n  return isDragging.x || isDragging.y;\n}\nexport { isDragActive, isDragging };", "map": {"version": 3, "names": ["isDragging", "x", "y", "isDragActive"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs"], "sourcesContent": ["const isDragging = {\n    x: false,\n    y: false,\n};\nfunction isDragActive() {\n    return isDragging.x || isDragging.y;\n}\n\nexport { isDragActive, isDragging };\n"], "mappings": "AAAA,MAAMA,UAAU,GAAG;EACfC,CAAC,EAAE,KAAK;EACRC,CAAC,EAAE;AACP,CAAC;AACD,SAASC,YAAYA,CAAA,EAAG;EACpB,OAAOH,UAAU,CAACC,CAAC,IAAID,UAAU,CAACE,CAAC;AACvC;AAEA,SAASC,YAAY,EAAEH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}