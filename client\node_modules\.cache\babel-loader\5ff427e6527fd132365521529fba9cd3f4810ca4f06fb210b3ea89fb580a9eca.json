{"ast": null, "code": "function calcChildStagger(children, child, delayChildren, staggerChildren = 0, staggerDirection = 1) {\n  const index = Array.from(children).sort((a, b) => a.sortNodePosition(b)).indexOf(child);\n  const numChildren = children.size;\n  const maxStaggerDuration = (numChildren - 1) * staggerChildren;\n  const delayIsFunction = typeof delayChildren === \"function\";\n  return delayIsFunction ? delayChildren(index, numChildren) : staggerDirection === 1 ? index * staggerChildren : maxStaggerDuration - index * staggerChildren;\n}\nexport { calcChildStagger };", "map": {"version": 3, "names": ["calcChildStagger", "children", "child", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON><PERSON>", "staggerDirection", "index", "Array", "from", "sort", "a", "b", "sortNodePosition", "indexOf", "numC<PERSON><PERSON>n", "size", "maxStaggerDuration", "delayIsFunction"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/animation/utils/calc-child-stagger.mjs"], "sourcesContent": ["function calcChildStagger(children, child, delayChildren, staggerChildren = 0, staggerDirection = 1) {\n    const index = Array.from(children)\n        .sort((a, b) => a.sortNodePosition(b))\n        .indexOf(child);\n    const numChildren = children.size;\n    const maxStaggerDuration = (numChildren - 1) * staggerChildren;\n    const delayIsFunction = typeof delayChildren === \"function\";\n    return delayIsFunction\n        ? delayChildren(index, numChildren)\n        : staggerDirection === 1\n            ? index * staggerChildren\n            : maxStaggerDuration - index * staggerChildren;\n}\n\nexport { calcChildStagger };\n"], "mappings": "AAAA,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,aAAa,EAAEC,eAAe,GAAG,CAAC,EAAEC,gBAAgB,GAAG,CAAC,EAAE;EACjG,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACP,QAAQ,CAAC,CAC7BQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,gBAAgB,CAACD,CAAC,CAAC,CAAC,CACrCE,OAAO,CAACX,KAAK,CAAC;EACnB,MAAMY,WAAW,GAAGb,QAAQ,CAACc,IAAI;EACjC,MAAMC,kBAAkB,GAAG,CAACF,WAAW,GAAG,CAAC,IAAIV,eAAe;EAC9D,MAAMa,eAAe,GAAG,OAAOd,aAAa,KAAK,UAAU;EAC3D,OAAOc,eAAe,GAChBd,aAAa,CAACG,KAAK,EAAEQ,WAAW,CAAC,GACjCT,gBAAgB,KAAK,CAAC,GAClBC,KAAK,GAAGF,eAAe,GACvBY,kBAAkB,GAAGV,KAAK,GAAGF,eAAe;AAC1D;AAEA,SAASJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}