{"ast": null, "code": "// Fixes https://github.com/motiondivision/motion/issues/2270\nconst getContextWindow = ({\n  current\n}) => {\n  return current ? current.ownerDocument.defaultView : null;\n};\nexport { getContextWindow };", "map": {"version": 3, "names": ["getContextWindow", "current", "ownerDocument", "defaultView"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/utils/get-context-window.mjs"], "sourcesContent": ["// Fixes https://github.com/motiondivision/motion/issues/2270\nconst getContextWindow = ({ current }) => {\n    return current ? current.ownerDocument.defaultView : null;\n};\n\nexport { getContextWindow };\n"], "mappings": "AAAA;AACA,MAAMA,gBAAgB,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACtC,OAAOA,OAAO,GAAGA,OAAO,CAACC,aAAa,CAACC,WAAW,GAAG,IAAI;AAC7D,CAAC;AAED,SAASH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}