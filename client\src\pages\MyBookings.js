import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Calendar, 
  Users, 
  Phone, 
  Mail, 
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';

const MyBookings = () => {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchBookings();
  }, []);

  const fetchBookings = async () => {
    try {
      const response = await fetch('http://localhost:5000/api/bookings');
      const result = await response.json();
      
      if (result.success) {
        setBookings(result.data);
      } else {
        setError('Failed to fetch bookings');
      }
    } catch (err) {
      setError('Error connecting to server');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'confirmed':
        return <CheckCircle style={{ width: '1.25rem', height: '1.25rem', color: 'var(--green-500)' }} />;
      case 'pending':
        return <Clock style={{ width: '1.25rem', height: '1.25rem', color: 'var(--yellow-500)' }} />;
      case 'cancelled':
        return <XCircle style={{ width: '1.25rem', height: '1.25rem', color: 'var(--red-500)' }} />;
      default:
        return <AlertCircle style={{ width: '1.25rem', height: '1.25rem', color: 'var(--gray-500)' }} />;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen py-12" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="container">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" 
                 style={{ borderColor: 'var(--primary-600)' }}></div>
            <p style={{ color: 'var(--text-secondary)' }}>Loading your bookings...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen py-12" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="container">
          <div className="text-center">
            <AlertCircle style={{ width: '3rem', height: '3rem', color: 'var(--red-500)', margin: '0 auto 1rem' }} />
            <h2 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
              Error Loading Bookings
            </h2>
            <p style={{ color: 'var(--text-secondary)' }}>{error}</p>
            <button 
              onClick={fetchBookings}
              className="btn btn-primary mt-4"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>
            My Bookings
          </h1>
          <p className="text-xl" style={{ color: 'var(--text-secondary)' }}>
            View and manage your hotel reservations
          </p>
        </motion.div>

        {bookings.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center py-12"
          >
            <Calendar style={{ width: '4rem', height: '4rem', color: 'var(--gray-400)', margin: '0 auto 1rem' }} />
            <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
              No Bookings Found
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              You haven't made any reservations yet.
            </p>
            <a href="/rooms" className="btn btn-primary">
              Browse Rooms
            </a>
          </motion.div>
        ) : (
          <div className="grid gap-6">
            {bookings.map((booking, index) => (
              <motion.div
                key={booking.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="card p-6"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                  {/* Booking Info */}
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-4">
                      <h3 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>
                        {booking.room}
                      </h3>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(booking.status)}
                        <span className={`status-badge status-${booking.status}`}>
                          {booking.status}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Calendar style={{ width: '1rem', height: '1rem', color: 'var(--text-secondary)' }} />
                        <div>
                          <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Check-in</p>
                          <p className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                            {formatDate(booking.checkIn)}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Calendar style={{ width: '1rem', height: '1rem', color: 'var(--text-secondary)' }} />
                        <div>
                          <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Check-out</p>
                          <p className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                            {formatDate(booking.checkOut)}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Users style={{ width: '1rem', height: '1rem', color: 'var(--text-secondary)' }} />
                        <div>
                          <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Guests</p>
                          <p className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                            {booking.guests || 1}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Clock style={{ width: '1rem', height: '1rem', color: 'var(--text-secondary)' }} />
                        <div>
                          <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Nights</p>
                          <p className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                            {booking.nights}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4 text-sm" style={{ color: 'var(--text-secondary)' }}>
                      <div className="flex items-center gap-1">
                        <Mail style={{ width: '0.875rem', height: '0.875rem' }} />
                        <span>{booking.guestEmail}</span>
                      </div>
                      {booking.guestPhone && (
                        <div className="flex items-center gap-1">
                          <Phone style={{ width: '0.875rem', height: '0.875rem' }} />
                          <span>{booking.guestPhone}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Booking Details */}
                  <div className="text-right">
                    <div className="mb-2">
                      <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                        Booking ID
                      </p>
                      <p className="font-mono font-semibold" style={{ color: 'var(--text-primary)' }}>
                        {booking.id}
                      </p>
                    </div>
                    <div className="mb-4">
                      <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                        Total Amount
                      </p>
                      <p className="text-2xl font-bold" style={{ color: 'var(--primary-600)' }}>
                        ${booking.totalAmount}
                      </p>
                    </div>
                    
                    {booking.status === 'confirmed' && (
                      <div className="flex flex-col gap-2">
                        <button className="btn btn-outline text-sm">
                          View Details
                        </button>
                        <button className="btn btn-secondary text-sm">
                          Modify Booking
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {booking.specialRequests && (
                  <div className="mt-4 pt-4 border-t" style={{ borderColor: 'var(--border-color)' }}>
                    <p className="text-sm font-medium mb-1" style={{ color: 'var(--text-secondary)' }}>
                      Special Requests:
                    </p>
                    <p className="text-sm" style={{ color: 'var(--text-primary)' }}>
                      {booking.specialRequests}
                    </p>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyBookings;
