{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hotel mangement\\\\client\\\\src\\\\pages\\\\Rooms.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { Users, Bed, Maximize, Star, Wifi, Tv, Coffee, Car, Wind, Eye, Calendar } from 'lucide-react';\nimport { rooms } from '../data/mockData';\nimport BookingModal from '../components/BookingModal';\nimport { cn } from '../lib/utils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Rooms = () => {\n  _s();\n  const [selectedRoom, setSelectedRoom] = useState(null);\n  const getAmenityIcon = amenity => {\n    const iconMap = {\n      'Free Wi-Fi': Wifi,\n      'Smart TV': Tv,\n      'TV': Tv,\n      'Mini Bar': Coffee,\n      'Room Service': Car,\n      'Air Conditioning': Wind,\n      'City View': Eye,\n      'Ocean View': Eye,\n      'Garden View': Eye,\n      'Panoramic View': Eye\n    };\n    return iconMap[amenity] || Coffee;\n  };\n  const handleReserve = roomId => {\n    console.log('Reserving room:', roomId);\n    // Here you would typically navigate to a booking form or open a modal\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 dark:bg-gray-900 py-12\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Our Rooms & Suites\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\",\n          children: \"Choose from our carefully designed rooms and suites, each offering comfort, luxury, and modern amenities for an unforgettable stay.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: rooms.map((room, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          className: \"bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative h-64 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: room.image,\n              alt: room.name,\n              className: \"w-full h-full object-cover hover:scale-105 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 right-4 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-full px-3 py-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Star, {\n                  className: \"h-4 w-4 text-yellow-400 fill-current\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: room.rating\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between items-start mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                  children: room.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 94,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                  children: room.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-3xl font-bold text-primary-600 dark:text-primary-400\",\n                  children: [\"$\", room.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-500 dark:text-gray-400\",\n                  children: \"per night\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(Users, {\n                  className: \"h-5 w-5 text-gray-600 dark:text-gray-300 mx-auto mb-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: [room.capacity, \" Guests\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(Bed, {\n                  className: \"h-5 w-5 text-gray-600 dark:text-gray-300 mx-auto mb-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: room.beds\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(Maximize, {\n                  className: \"h-5 w-5 text-gray-600 dark:text-gray-300 mx-auto mb-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                  children: room.size\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-semibold text-gray-900 dark:text-white mb-3\",\n                children: \"Amenities\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2\",\n                children: [room.amenities.slice(0, 6).map((amenity, idx) => {\n                  const IconComponent = getAmenityIcon(amenity);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-full text-xs\",\n                    children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                      className: \"h-3 w-3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: amenity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 147,\n                      columnNumber: 27\n                    }, this)]\n                  }, idx, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 25\n                  }, this);\n                }), room.amenities.length > 6 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full text-xs\",\n                  children: [\"+\", room.amenities.length - 6, \" more\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleReserve(room.id),\n              className: \"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                className: \"h-5 w-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Reserve Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 15\n          }, this)]\n        }, room.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        viewport: {\n          once: true\n        },\n        className: \"mt-16 bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n          children: \"Need Help Choosing?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 dark:text-gray-300 mb-6\",\n          children: \"Our team is here to help you find the perfect room for your stay. Contact us for personalized recommendations based on your preferences and needs.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200\",\n            children: \"Call Us: +1 (555) 123-4567\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"inline-flex items-center px-6 py-3 border border-primary-600 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 font-medium rounded-lg transition-colors duration-200\",\n            children: \"Live Chat Support\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n_s(Rooms, \"gCF8VQh5LuJb+cswK4atwpGFwEU=\");\n_c = Rooms;\nexport default Rooms;\nvar _c;\n$RefreshReg$(_c, \"Rooms\");", "map": {"version": 3, "names": ["React", "useState", "motion", "Users", "Bed", "Maximize", "Star", "Wifi", "Tv", "Coffee", "Car", "Wind", "Eye", "Calendar", "rooms", "BookingModal", "cn", "jsxDEV", "_jsxDEV", "Rooms", "_s", "selected<PERSON><PERSON>", "setSelectedRoom", "getAmenityIcon", "amenity", "iconMap", "handleReserve", "roomId", "console", "log", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "room", "index", "delay", "src", "image", "alt", "name", "rating", "description", "price", "capacity", "beds", "size", "amenities", "slice", "idx", "IconComponent", "length", "onClick", "id", "whileInView", "viewport", "once", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/src/pages/Rooms.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Users,\n  Bed,\n  Maximize,\n  Star,\n  Wifi,\n  Tv,\n  Coffee,\n  Car,\n  Wind,\n  Eye,\n  Calendar\n} from 'lucide-react';\nimport { rooms } from '../data/mockData';\nimport BookingModal from '../components/BookingModal';\nimport { cn } from '../lib/utils';\n\nconst Rooms = () => {\n  const [selectedRoom, setSelectedRoom] = useState(null);\n\n  const getAmenityIcon = (amenity) => {\n    const iconMap = {\n      'Free Wi-Fi': Wifi,\n      'Smart TV': Tv,\n      'TV': Tv,\n      'Mini Bar': Coffee,\n      'Room Service': Car,\n      'Air Conditioning': Wind,\n      'City View': Eye,\n      'Ocean View': Eye,\n      'Garden View': Eye,\n      'Panoramic View': Eye,\n    };\n    return iconMap[amenity] || Coffee;\n  };\n\n  const handleReserve = (roomId) => {\n    console.log('Reserving room:', roomId);\n    // Here you would typically navigate to a booking form or open a modal\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-12\"\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4\">\n            Our Rooms & Suites\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto\">\n            Choose from our carefully designed rooms and suites, each offering comfort, \n            luxury, and modern amenities for an unforgettable stay.\n          </p>\n        </motion.div>\n\n        {/* Rooms Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {rooms.map((room, index) => (\n            <motion.div\n              key={room.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              className=\"bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300\"\n            >\n              {/* Room Image */}\n              <div className=\"relative h-64 overflow-hidden\">\n                <img\n                  src={room.image}\n                  alt={room.name}\n                  className=\"w-full h-full object-cover hover:scale-105 transition-transform duration-300\"\n                />\n                <div className=\"absolute top-4 right-4 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-full px-3 py-1\">\n                  <div className=\"flex items-center space-x-1\">\n                    <Star className=\"h-4 w-4 text-yellow-400 fill-current\" />\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {room.rating}\n                    </span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Room Details */}\n              <div className=\"p-6\">\n                <div className=\"flex justify-between items-start mb-4\">\n                  <div>\n                    <h3 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                      {room.name}\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\n                      {room.description}\n                    </p>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-3xl font-bold text-primary-600 dark:text-primary-400\">\n                      ${room.price}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      per night\n                    </div>\n                  </div>\n                </div>\n\n                {/* Room Info */}\n                <div className=\"grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div className=\"text-center\">\n                    <Users className=\"h-5 w-5 text-gray-600 dark:text-gray-300 mx-auto mb-1\" />\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {room.capacity} Guests\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <Bed className=\"h-5 w-5 text-gray-600 dark:text-gray-300 mx-auto mb-1\" />\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {room.beds}\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <Maximize className=\"h-5 w-5 text-gray-600 dark:text-gray-300 mx-auto mb-1\" />\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {room.size}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Amenities */}\n                <div className=\"mb-6\">\n                  <h4 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-3\">\n                    Amenities\n                  </h4>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {room.amenities.slice(0, 6).map((amenity, idx) => {\n                      const IconComponent = getAmenityIcon(amenity);\n                      return (\n                        <div\n                          key={idx}\n                          className=\"flex items-center space-x-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 px-3 py-1 rounded-full text-xs\"\n                        >\n                          <IconComponent className=\"h-3 w-3\" />\n                          <span>{amenity}</span>\n                        </div>\n                      );\n                    })}\n                    {room.amenities.length > 6 && (\n                      <div className=\"flex items-center bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-3 py-1 rounded-full text-xs\">\n                        +{room.amenities.length - 6} more\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Reserve Button */}\n                <button\n                  onClick={() => handleReserve(room.id)}\n                  className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n                >\n                  <Calendar className=\"h-5 w-5\" />\n                  <span>Reserve Now</span>\n                </button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-16 bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg\"\n        >\n          <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-4\">\n            Need Help Choosing?\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-300 mb-6\">\n            Our team is here to help you find the perfect room for your stay. \n            Contact us for personalized recommendations based on your preferences and needs.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <button className=\"inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200\">\n              Call Us: +1 (555) 123-4567\n            </button>\n            <button className=\"inline-flex items-center px-6 py-3 border border-primary-600 text-primary-600 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 font-medium rounded-lg transition-colors duration-200\">\n              Live Chat Support\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default Rooms;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,KAAK,EACLC,GAAG,EACHC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,EAAE,EACFC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,GAAG,EACHC,QAAQ,QACH,cAAc;AACrB,SAASC,KAAK,QAAQ,kBAAkB;AACxC,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,EAAE,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMsB,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,OAAO,GAAG;MACd,YAAY,EAAElB,IAAI;MAClB,UAAU,EAAEC,EAAE;MACd,IAAI,EAAEA,EAAE;MACR,UAAU,EAAEC,MAAM;MAClB,cAAc,EAAEC,GAAG;MACnB,kBAAkB,EAAEC,IAAI;MACxB,WAAW,EAAEC,GAAG;MAChB,YAAY,EAAEA,GAAG;MACjB,aAAa,EAAEA,GAAG;MAClB,gBAAgB,EAAEA;IACpB,CAAC;IACD,OAAOa,OAAO,CAACD,OAAO,CAAC,IAAIf,MAAM;EACnC,CAAC;EAED,MAAMiB,aAAa,GAAIC,MAAM,IAAK;IAChCC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,MAAM,CAAC;IACtC;EACF,CAAC;EAED,oBACET,OAAA;IAAKY,SAAS,EAAC,gDAAgD;IAAAC,QAAA,eAC7Db,OAAA;MAAKY,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBAErDb,OAAA,CAAChB,MAAM,CAAC8B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7Bb,OAAA;UAAIY,SAAS,EAAC,mEAAmE;UAAAC,QAAA,EAAC;QAElF;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxB,OAAA;UAAGY,SAAS,EAAC,4DAA4D;UAAAC,QAAA,EAAC;QAG1E;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbxB,OAAA;QAAKY,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EACnDjB,KAAK,CAAC6B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrB3B,OAAA,CAAChB,MAAM,CAAC8B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEQ,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAClDf,SAAS,EAAC,gHAAgH;UAAAC,QAAA,gBAG1Hb,OAAA;YAAKY,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5Cb,OAAA;cACE6B,GAAG,EAAEH,IAAI,CAACI,KAAM;cAChBC,GAAG,EAAEL,IAAI,CAACM,IAAK;cACfpB,SAAS,EAAC;YAA8E;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACFxB,OAAA;cAAKY,SAAS,EAAC,gGAAgG;cAAAC,QAAA,eAC7Gb,OAAA;gBAAKY,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1Cb,OAAA,CAACZ,IAAI;kBAACwB,SAAS,EAAC;gBAAsC;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzDxB,OAAA;kBAAMY,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAChEa,IAAI,CAACO;gBAAM;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxB,OAAA;YAAKY,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBb,OAAA;cAAKY,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDb,OAAA;gBAAAa,QAAA,gBACEb,OAAA;kBAAIY,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,EAClEa,IAAI,CAACM;gBAAI;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACLxB,OAAA;kBAAGY,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EACpDa,IAAI,CAACQ;gBAAW;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACNxB,OAAA;gBAAKY,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBb,OAAA;kBAAKY,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,GAAC,GACxE,EAACa,IAAI,CAACS,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eACNxB,OAAA;kBAAKY,SAAS,EAAC,0CAA0C;kBAAAC,QAAA,EAAC;gBAE1D;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxB,OAAA;cAAKY,SAAS,EAAC,wEAAwE;cAAAC,QAAA,gBACrFb,OAAA;gBAAKY,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1Bb,OAAA,CAACf,KAAK;kBAAC2B,SAAS,EAAC;gBAAuD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3ExB,OAAA;kBAAKY,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAC/Da,IAAI,CAACU,QAAQ,EAAC,SACjB;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxB,OAAA;gBAAKY,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1Bb,OAAA,CAACd,GAAG;kBAAC0B,SAAS,EAAC;gBAAuD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACzExB,OAAA;kBAAKY,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC/Da,IAAI,CAACW;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNxB,OAAA;gBAAKY,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1Bb,OAAA,CAACb,QAAQ;kBAACyB,SAAS,EAAC;gBAAuD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9ExB,OAAA;kBAAKY,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC/Da,IAAI,CAACY;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxB,OAAA;cAAKY,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBb,OAAA;gBAAIY,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,EAAC;cAEzE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxB,OAAA;gBAAKY,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,GAClCa,IAAI,CAACa,SAAS,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACf,GAAG,CAAC,CAACnB,OAAO,EAAEmC,GAAG,KAAK;kBAChD,MAAMC,aAAa,GAAGrC,cAAc,CAACC,OAAO,CAAC;kBAC7C,oBACEN,OAAA;oBAEEY,SAAS,EAAC,wIAAwI;oBAAAC,QAAA,gBAElJb,OAAA,CAAC0C,aAAa;sBAAC9B,SAAS,EAAC;oBAAS;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrCxB,OAAA;sBAAAa,QAAA,EAAOP;oBAAO;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAJjBiB,GAAG;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAKL,CAAC;gBAEV,CAAC,CAAC,EACDE,IAAI,CAACa,SAAS,CAACI,MAAM,GAAG,CAAC,iBACxB3C,OAAA;kBAAKY,SAAS,EAAC,gHAAgH;kBAAAC,QAAA,GAAC,GAC7H,EAACa,IAAI,CAACa,SAAS,CAACI,MAAM,GAAG,CAAC,EAAC,OAC9B;gBAAA;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxB,OAAA;cACE4C,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAACkB,IAAI,CAACmB,EAAE,CAAE;cACtCjC,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAE5Kb,OAAA,CAACL,QAAQ;gBAACiB,SAAS,EAAC;cAAS;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChCxB,OAAA;gBAAAa,QAAA,EAAM;cAAW;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,GApGDE,IAAI,CAACmB,EAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqGF,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNxB,OAAA,CAAChB,MAAM,CAAC8B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/B6B,WAAW,EAAE;UAAE9B,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9B2B,QAAQ,EAAE;UAAEC,IAAI,EAAE;QAAK,CAAE;QACzBpC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBAErEb,OAAA;UAAIY,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAEtE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxB,OAAA;UAAGY,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAGrD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJxB,OAAA;UAAKY,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC9Cb,OAAA;YAAQY,SAAS,EAAC,yIAAyI;YAAAC,QAAA,EAAC;UAE5J;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxB,OAAA;YAAQY,SAAS,EAAC,4MAA4M;YAAAC,QAAA,EAAC;UAE/N;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtB,EAAA,CAnLID,KAAK;AAAAgD,EAAA,GAALhD,KAAK;AAqLX,eAAeA,KAAK;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}