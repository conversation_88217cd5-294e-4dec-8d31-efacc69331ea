{"ast": null, "code": "/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = v => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\nexport { isNumericalString };", "map": {"version": 3, "names": ["isNumericalString", "v", "test"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-utils/dist/es/is-numerical-string.mjs"], "sourcesContent": ["/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,iBAAiB,GAAIC,CAAC,IAAK,8BAA8B,CAACC,IAAI,CAACD,CAAC,CAAC;AAEvE,SAASD,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}