[{"C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Rooms.js": "4", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Facilities.js": "5", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Home.js": "6", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Contact.js": "7", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Gallery.js": "8", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Dashboard.js": "9", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Footer.js": "10", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Navbar.js": "11", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\contexts\\ThemeContext.js": "12", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\data\\mockData.js": "13", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\lib\\utils.js": "14"}, {"size": 535, "mtime": 1755934078871, "results": "15", "hashOfConfig": "16"}, {"size": 1178, "mtime": 1755934891342, "results": "17", "hashOfConfig": "16"}, {"size": 362, "mtime": 1755934079432, "results": "18", "hashOfConfig": "16"}, {"size": 8105, "mtime": 1755934990973, "results": "19", "hashOfConfig": "16"}, {"size": 8202, "mtime": 1755935018527, "results": "20", "hashOfConfig": "16"}, {"size": 9289, "mtime": 1755934964012, "results": "21", "hashOfConfig": "16"}, {"size": 10304, "mtime": 1755935078710, "results": "22", "hashOfConfig": "16"}, {"size": 7288, "mtime": 1755935044722, "results": "23", "hashOfConfig": "16"}, {"size": 10719, "mtime": 1755935115022, "results": "24", "hashOfConfig": "16"}, {"size": 3982, "mtime": 1755934931971, "results": "25", "hashOfConfig": "16"}, {"size": 4456, "mtime": 1755936869199, "results": "26", "hashOfConfig": "16"}, {"size": 1253, "mtime": 1755934879050, "results": "27", "hashOfConfig": "16"}, {"size": 7833, "mtime": 1755934870054, "results": "28", "hashOfConfig": "16"}, {"size": 87, "mtime": 1755936810529, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7z2j1d", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Rooms.js", ["72", "73"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Facilities.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Home.js", ["74", "75"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Gallery.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Dashboard.js", ["76", "77"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Footer.js", ["78", "79", "80"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\data\\mockData.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\lib\\utils.js", [], [], {"ruleId": "81", "severity": 1, "message": "82", "line": 19, "column": 10, "nodeType": "83", "messageId": "84", "endLine": 19, "endColumn": 22}, {"ruleId": "81", "severity": 1, "message": "85", "line": 19, "column": 24, "nodeType": "83", "messageId": "84", "endLine": 19, "endColumn": 39}, {"ruleId": "81", "severity": 1, "message": "86", "line": 6, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 6, "endColumn": 11}, {"ruleId": "81", "severity": 1, "message": "87", "line": 7, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 7, "endColumn": 8}, {"ruleId": "81", "severity": 1, "message": "88", "line": 7, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 7, "endColumn": 13}, {"ruleId": "81", "severity": 1, "message": "89", "line": 9, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 9, "endColumn": 8}, {"ruleId": "90", "severity": 1, "message": "91", "line": 83, "column": 15, "nodeType": "92", "endLine": 83, "endColumn": 102}, {"ruleId": "90", "severity": 1, "message": "91", "line": 86, "column": 15, "nodeType": "92", "endLine": 86, "endColumn": 102}, {"ruleId": "90", "severity": 1, "message": "91", "line": 89, "column": 15, "nodeType": "92", "endLine": 89, "endColumn": 102}, "no-unused-vars", "'selectedRoom' is assigned a value but never used.", "Identifier", "unusedVar", "'setSelectedRoom' is assigned a value but never used.", "'Calendar' is defined but never used.", "'Users' is defined but never used.", "'DollarSign' is defined but never used.", "'Clock' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]