[{"C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Rooms.js": "4", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Facilities.js": "5", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Home.js": "6", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Contact.js": "7", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Gallery.js": "8", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Dashboard.js": "9", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Footer.js": "10", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Navbar.js": "11", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\contexts\\ThemeContext.js": "12", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\data\\mockData.js": "13", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\lib\\utils.js": "14", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\contexts\\BookingContext.js": "15", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\BookingModal.js": "16", "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\MyBookings.js": "17"}, {"size": 535, "mtime": 1755934078871, "results": "18", "hashOfConfig": "19"}, {"size": 1485, "mtime": 1755938152135, "results": "20", "hashOfConfig": "19"}, {"size": 362, "mtime": 1755934079432, "results": "21", "hashOfConfig": "19"}, {"size": 7103, "mtime": 1755938004351, "results": "22", "hashOfConfig": "19"}, {"size": 8202, "mtime": 1755935018527, "results": "23", "hashOfConfig": "19"}, {"size": 9931, "mtime": 1755938036580, "results": "24", "hashOfConfig": "19"}, {"size": 10304, "mtime": 1755935078710, "results": "25", "hashOfConfig": "19"}, {"size": 7288, "mtime": 1755935044722, "results": "26", "hashOfConfig": "19"}, {"size": 10719, "mtime": 1755935115022, "results": "27", "hashOfConfig": "19"}, {"size": 3982, "mtime": 1755934931971, "results": "28", "hashOfConfig": "19"}, {"size": 4507, "mtime": 1755938165975, "results": "29", "hashOfConfig": "19"}, {"size": 1253, "mtime": 1755934879050, "results": "30", "hashOfConfig": "19"}, {"size": 7833, "mtime": 1755934870054, "results": "31", "hashOfConfig": "19"}, {"size": 87, "mtime": 1755936810529, "results": "32", "hashOfConfig": "19"}, {"size": 5230, "mtime": 1755937788715, "results": "33", "hashOfConfig": "19"}, {"size": 16139, "mtime": 1755937846027, "results": "34", "hashOfConfig": "19"}, {"size": 10622, "mtime": 1755938126095, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7z2j1d", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Rooms.js", ["87"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Facilities.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Home.js", ["88", "89"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Gallery.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\Dashboard.js", ["90", "91"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Footer.js", ["92", "93", "94"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\contexts\\ThemeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\data\\mockData.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\lib\\utils.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\contexts\\BookingContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\components\\BookingModal.js", ["95", "96", "97"], [], "C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\src\\pages\\MyBookings.js", ["98"], [], {"ruleId": "99", "severity": 1, "message": "100", "line": 18, "column": 10, "nodeType": "101", "messageId": "102", "endLine": 18, "endColumn": 12}, {"ruleId": "99", "severity": 1, "message": "103", "line": 6, "column": 3, "nodeType": "101", "messageId": "102", "endLine": 6, "endColumn": 11}, {"ruleId": "99", "severity": 1, "message": "104", "line": 7, "column": 3, "nodeType": "101", "messageId": "102", "endLine": 7, "endColumn": 8}, {"ruleId": "99", "severity": 1, "message": "105", "line": 7, "column": 3, "nodeType": "101", "messageId": "102", "endLine": 7, "endColumn": 13}, {"ruleId": "99", "severity": 1, "message": "106", "line": 9, "column": 3, "nodeType": "101", "messageId": "102", "endLine": 9, "endColumn": 8}, {"ruleId": "107", "severity": 1, "message": "108", "line": 83, "column": 15, "nodeType": "109", "endLine": 83, "endColumn": 102}, {"ruleId": "107", "severity": 1, "message": "108", "line": 86, "column": 15, "nodeType": "109", "endLine": 86, "endColumn": 102}, {"ruleId": "107", "severity": 1, "message": "108", "line": 89, "column": 15, "nodeType": "109", "endLine": 89, "endColumn": 102}, {"ruleId": "99", "severity": 1, "message": "103", "line": 3, "column": 13, "nodeType": "101", "messageId": "102", "endLine": 3, "endColumn": 21}, {"ruleId": "99", "severity": 1, "message": "104", "line": 3, "column": 23, "nodeType": "101", "messageId": "102", "endLine": 3, "endColumn": 28}, {"ruleId": "99", "severity": 1, "message": "110", "line": 3, "column": 30, "nodeType": "101", "messageId": "102", "endLine": 3, "endColumn": 40}, {"ruleId": "99", "severity": 1, "message": "111", "line": 8, "column": 3, "nodeType": "101", "messageId": "102", "endLine": 8, "endColumn": 9}, "no-unused-vars", "'cn' is defined but never used.", "Identifier", "unusedVar", "'Calendar' is defined but never used.", "'Users' is defined but never used.", "'DollarSign' is defined but never used.", "'Clock' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'CreditCard' is defined but never used.", "'MapPin' is defined but never used."]