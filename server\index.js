const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data (in a real app, this would come from MongoDB)
const mockData = {
  rooms: [
    {
      id: 1,
      name: "Deluxe King Room",
      price: 299,
      capacity: 2,
      beds: "1 King Bed",
      size: "35 sqm",
      rating: 4.8,
      image: "https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=500&h=300&fit=crop",
      amenities: ["Free Wi-Fi", "Smart TV", "Mini Bar", "Room Service", "Air Conditioning", "City View"],
      description: "Spacious king room with modern amenities and stunning city views.",
      available: true
    },
    {
      id: 2,
      name: "Executive Suite",
      price: 499,
      capacity: 4,
      beds: "1 King + 1 Sofa Bed",
      size: "65 sqm",
      rating: 4.9,
      image: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=500&h=300&fit=crop",
      amenities: ["Free Wi-Fi", "Smart TV", "Mini Bar", "Room Service", "Air Conditioning", "Ocean View", "Balcony", "Work Desk"],
      description: "Luxurious suite with separate living area and premium ocean views.",
      available: true
    }
    // Add more rooms as needed
  ],
  
  bookings: [
    {
      id: "BK001",
      guestName: "John Smith",
      room: "Deluxe King Room",
      roomId: 1,
      nights: 3,
      checkIn: "2024-01-15",
      checkOut: "2024-01-18",
      status: "confirmed",
      totalAmount: 897,
      guestEmail: "<EMAIL>",
      guestPhone: "******-0123"
    }
    // Add more bookings as needed
  ],
  
  facilities: [
    {
      id: 1,
      name: "Infinity Pool",
      description: "Stunning rooftop infinity pool with panoramic city views",
      image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop",
      hours: "6:00 AM - 10:00 PM",
      features: ["Heated Pool", "Pool Bar", "Lounge Chairs", "Towel Service"]
    }
    // Add more facilities as needed
  ]
};

// API Routes

// Get all rooms
app.get('/api/rooms', (req, res) => {
  try {
    const { available, capacity, priceRange } = req.query;
    let filteredRooms = [...mockData.rooms];
    
    if (available === 'true') {
      filteredRooms = filteredRooms.filter(room => room.available);
    }
    
    if (capacity) {
      filteredRooms = filteredRooms.filter(room => room.capacity >= parseInt(capacity));
    }
    
    if (priceRange) {
      const [min, max] = priceRange.split('-').map(Number);
      filteredRooms = filteredRooms.filter(room => room.price >= min && room.price <= max);
    }
    
    res.json({
      success: true,
      data: filteredRooms,
      count: filteredRooms.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching rooms',
      error: error.message
    });
  }
});

// Get single room
app.get('/api/rooms/:id', (req, res) => {
  try {
    const roomId = parseInt(req.params.id);
    const room = mockData.rooms.find(r => r.id === roomId);
    
    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }
    
    res.json({
      success: true,
      data: room
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching room',
      error: error.message
    });
  }
});

// Get all bookings
app.get('/api/bookings', (req, res) => {
  try {
    const { status, date } = req.query;
    let filteredBookings = [...mockData.bookings];
    
    if (status) {
      filteredBookings = filteredBookings.filter(booking => booking.status === status);
    }
    
    if (date) {
      filteredBookings = filteredBookings.filter(booking => 
        booking.checkIn <= date && booking.checkOut >= date
      );
    }
    
    res.json({
      success: true,
      data: filteredBookings,
      count: filteredBookings.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching bookings',
      error: error.message
    });
  }
});

// Create new booking
app.post('/api/bookings', (req, res) => {
  try {
    const {
      guestName,
      guestEmail,
      guestPhone,
      roomId,
      checkIn,
      checkOut,
      nights,
      totalAmount,
      specialRequests,
      guests
    } = req.body;

    // Validate required fields
    if (!guestName || !guestEmail || !roomId || !checkIn || !checkOut) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: guestName, guestEmail, roomId, checkIn, checkOut'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(guestEmail)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }

    // Validate dates
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (checkInDate < today) {
      return res.status(400).json({
        success: false,
        message: 'Check-in date cannot be in the past'
      });
    }

    if (checkOutDate <= checkInDate) {
      return res.status(400).json({
        success: false,
        message: 'Check-out date must be after check-in date'
      });
    }

    // Find the room
    const room = mockData.rooms.find(r => r.id === parseInt(roomId));
    if (!room) {
      return res.status(404).json({
        success: false,
        message: 'Room not found'
      });
    }

    // Check room availability (simplified - in real app, check against existing bookings)
    const existingBooking = mockData.bookings.find(booking =>
      booking.roomId === parseInt(roomId) &&
      booking.status === 'confirmed' &&
      ((checkInDate >= new Date(booking.checkIn) && checkInDate < new Date(booking.checkOut)) ||
       (checkOutDate > new Date(booking.checkIn) && checkOutDate <= new Date(booking.checkOut)) ||
       (checkInDate <= new Date(booking.checkIn) && checkOutDate >= new Date(booking.checkOut)))
    );

    if (existingBooking) {
      return res.status(409).json({
        success: false,
        message: 'Room is not available for the selected dates'
      });
    }

    // Generate booking ID
    const bookingId = `BK${String(mockData.bookings.length + 1).padStart(3, '0')}`;

    // Calculate nights if not provided
    const calculatedNights = nights || Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24));

    // Calculate total amount if not provided
    const calculatedTotal = totalAmount || (room.price * calculatedNights);

    // Create new booking
    const newBooking = {
      id: bookingId,
      guestName,
      guestEmail,
      guestPhone: guestPhone || '',
      room: room.name,
      roomId: parseInt(roomId),
      nights: calculatedNights,
      checkIn,
      checkOut,
      status: 'confirmed',
      totalAmount: calculatedTotal,
      specialRequests: specialRequests || '',
      guests: guests || 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Add to mock data (in real app, save to database)
    mockData.bookings.push(newBooking);

    // Log the booking creation
    console.log(`New booking created: ${bookingId} for ${guestName}`);

    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      data: newBooking
    });
  } catch (error) {
    console.error('Error creating booking:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating booking',
      error: error.message
    });
  }
});

// Get all facilities
app.get('/api/facilities', (req, res) => {
  try {
    res.json({
      success: true,
      data: mockData.facilities,
      count: mockData.facilities.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching facilities',
      error: error.message
    });
  }
});

// Contact form submission
app.post('/api/contact', (req, res) => {
  try {
    const { name, email, subject, message } = req.body;
    
    // Validate required fields
    if (!name || !email || !subject || !message) {
      return res.status(400).json({
        success: false,
        message: 'All fields are required'
      });
    }
    
    // In a real app, you would save this to database and/or send email
    console.log('Contact form submission:', { name, email, subject, message });
    
    res.json({
      success: true,
      message: 'Message sent successfully. We will get back to you within 2 hours.'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error sending message',
      error: error.message
    });
  }
});

// Dashboard stats
app.get('/api/dashboard/stats', (req, res) => {
  try {
    const totalRooms = mockData.rooms.length;
    const occupiedRooms = mockData.bookings.filter(b => b.status === 'confirmed').length;
    const availableRooms = totalRooms - occupiedRooms;
    const todayCheckIns = mockData.bookings.filter(b => 
      new Date(b.checkIn).toDateString() === new Date().toDateString()
    ).length;
    
    const stats = {
      totalRooms,
      occupiedRooms,
      availableRooms,
      todayCheckIns,
      revenue: {
        today: 15420,
        month: 342800,
        year: 2847600
      }
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching dashboard stats',
      error: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Hotel Management API is running',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'API endpoint not found'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🏨 Hotel Management Server running on port ${PORT}`);
  console.log(`📍 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🔍 Health Check: http://localhost:${PORT}/api/health`);
});
