{"ast": null, "code": "import { complex } from './index.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\n\n/**\n * Properties that should default to 1 or 100%\n */\nconst maxDefaults = new Set([\"brightness\", \"contrast\", \"saturate\", \"opacity\"]);\nfunction applyDefaultFilter(v) {\n  const [name, value] = v.slice(0, -1).split(\"(\");\n  if (name === \"drop-shadow\") return v;\n  const [number] = value.match(floatRegex) || [];\n  if (!number) return v;\n  const unit = value.replace(number, \"\");\n  let defaultValue = maxDefaults.has(name) ? 1 : 0;\n  if (number !== value) defaultValue *= 100;\n  return name + \"(\" + defaultValue + unit + \")\";\n}\nconst functionRegex = /\\b([a-z-]*)\\(.*?\\)/gu;\nconst filter = {\n  ...complex,\n  getAnimatableNone: v => {\n    const functions = v.match(functionRegex);\n    return functions ? functions.map(applyDefaultFilter).join(\" \") : v;\n  }\n};\nexport { filter };", "map": {"version": 3, "names": ["complex", "floatRegex", "max<PERSON><PERSON><PERSON>s", "Set", "applyDefaultFilter", "v", "name", "value", "slice", "split", "number", "match", "unit", "replace", "defaultValue", "has", "functionRegex", "filter", "getAnimatableNone", "functions", "map", "join"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/value/types/complex/filter.mjs"], "sourcesContent": ["import { complex } from './index.mjs';\nimport { floatRegex } from '../utils/float-regex.mjs';\n\n/**\n * Properties that should default to 1 or 100%\n */\nconst maxDefaults = new Set([\"brightness\", \"contrast\", \"saturate\", \"opacity\"]);\nfunction applyDefaultFilter(v) {\n    const [name, value] = v.slice(0, -1).split(\"(\");\n    if (name === \"drop-shadow\")\n        return v;\n    const [number] = value.match(floatRegex) || [];\n    if (!number)\n        return v;\n    const unit = value.replace(number, \"\");\n    let defaultValue = maxDefaults.has(name) ? 1 : 0;\n    if (number !== value)\n        defaultValue *= 100;\n    return name + \"(\" + defaultValue + unit + \")\";\n}\nconst functionRegex = /\\b([a-z-]*)\\(.*?\\)/gu;\nconst filter = {\n    ...complex,\n    getAnimatableNone: (v) => {\n        const functions = v.match(functionRegex);\n        return functions ? functions.map(applyDefaultFilter).join(\" \") : v;\n    },\n};\n\nexport { filter };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASC,UAAU,QAAQ,0BAA0B;;AAErD;AACA;AACA;AACA,MAAMC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAC9E,SAASC,kBAAkBA,CAACC,CAAC,EAAE;EAC3B,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAGF,CAAC,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;EAC/C,IAAIH,IAAI,KAAK,aAAa,EACtB,OAAOD,CAAC;EACZ,MAAM,CAACK,MAAM,CAAC,GAAGH,KAAK,CAACI,KAAK,CAACV,UAAU,CAAC,IAAI,EAAE;EAC9C,IAAI,CAACS,MAAM,EACP,OAAOL,CAAC;EACZ,MAAMO,IAAI,GAAGL,KAAK,CAACM,OAAO,CAACH,MAAM,EAAE,EAAE,CAAC;EACtC,IAAII,YAAY,GAAGZ,WAAW,CAACa,GAAG,CAACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EAChD,IAAII,MAAM,KAAKH,KAAK,EAChBO,YAAY,IAAI,GAAG;EACvB,OAAOR,IAAI,GAAG,GAAG,GAAGQ,YAAY,GAAGF,IAAI,GAAG,GAAG;AACjD;AACA,MAAMI,aAAa,GAAG,sBAAsB;AAC5C,MAAMC,MAAM,GAAG;EACX,GAAGjB,OAAO;EACVkB,iBAAiB,EAAGb,CAAC,IAAK;IACtB,MAAMc,SAAS,GAAGd,CAAC,CAACM,KAAK,CAACK,aAAa,CAAC;IACxC,OAAOG,SAAS,GAAGA,SAAS,CAACC,GAAG,CAAChB,kBAAkB,CAAC,CAACiB,IAAI,CAAC,GAAG,CAAC,GAAGhB,CAAC;EACtE;AACJ,CAAC;AAED,SAASY,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}