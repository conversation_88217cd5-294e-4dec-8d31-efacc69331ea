{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"76otgf\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12.01\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"1dp563\"\n}]];\nconst Tablet = createLucideIcon(\"tablet\", __iconNode);\nexport { __iconNode, Tablet as default };", "map": {"version": 3, "names": ["__iconNode", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2", "Tablet", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\tablet.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', ry: '2', key: '76otgf' }],\n  ['line', { x1: '12', x2: '12.01', y1: '18', y2: '18', key: '1dp563' }],\n];\n\n/**\n * @component @name Tablet\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE4IiB5Mj0iMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/tablet\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tablet = createLucideIcon('tablet', __iconNode);\n\nexport default Tablet;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,KAAA,EAAO;EAAMC,MAAA,EAAQ;EAAMC,CAAA,EAAG;EAAKC,CAAA,EAAG;EAAKC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,QAAQ;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAASC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMJ,GAAA,EAAK;AAAA,CAAU,EACvE;AAaA,MAAMK,MAAA,GAASC,gBAAA,CAAiB,UAAUb,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}