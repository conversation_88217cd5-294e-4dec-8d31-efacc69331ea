{"ast": null, "code": "import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\nconst easingLookup = {\n  linear: noop,\n  easeIn,\n  easeInOut,\n  easeOut,\n  circIn,\n  circInOut,\n  circOut,\n  backIn,\n  backInOut,\n  backOut,\n  anticipate\n};\nconst isValidEasing = easing => {\n  return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = definition => {\n  if (isBezierDefinition(definition)) {\n    // If cubic bezier definition, create bezier curve\n    invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`, \"cubic-bezier-length\");\n    const [x1, y1, x2, y2] = definition;\n    return cubicBezier(x1, y1, x2, y2);\n  } else if (isValidEasing(definition)) {\n    // Else lookup from table\n    invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`, \"invalid-easing-type\");\n    return easingLookup[definition];\n  }\n  return definition;\n};\nexport { easingDefinitionToFunction };", "map": {"version": 3, "names": ["invariant", "noop", "anticipate", "backIn", "backInOut", "backOut", "circIn", "circInOut", "circOut", "cubicBezier", "easeIn", "easeInOut", "easeOut", "isBezierDefinition", "easingLookup", "linear", "isValidEasing", "easing", "easingDefinitionToFunction", "definition", "length", "x1", "y1", "x2", "y2", "undefined"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-utils/dist/es/easing/utils/map.mjs"], "sourcesContent": ["import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`, \"cubic-bezier-length\");\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`, \"invalid-easing-type\");\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,aAAa;AACxD,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,aAAa;AACxD,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,MAAM,EAAEC,SAAS,EAAEC,OAAO,QAAQ,aAAa;AACxD,SAASC,kBAAkB,QAAQ,4BAA4B;AAE/D,MAAMC,YAAY,GAAG;EACjBC,MAAM,EAAEd,IAAI;EACZS,MAAM;EACNC,SAAS;EACTC,OAAO;EACPN,MAAM;EACNC,SAAS;EACTC,OAAO;EACPL,MAAM;EACNC,SAAS;EACTC,OAAO;EACPH;AACJ,CAAC;AACD,MAAMc,aAAa,GAAIC,MAAM,IAAK;EAC9B,OAAO,OAAOA,MAAM,KAAK,QAAQ;AACrC,CAAC;AACD,MAAMC,0BAA0B,GAAIC,UAAU,IAAK;EAC/C,IAAIN,kBAAkB,CAACM,UAAU,CAAC,EAAE;IAChC;IACAnB,SAAS,CAACmB,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE,yDAAyD,EAAE,qBAAqB,CAAC;IACpH,MAAM,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,GAAGL,UAAU;IACnC,OAAOV,WAAW,CAACY,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EACtC,CAAC,MACI,IAAIR,aAAa,CAACG,UAAU,CAAC,EAAE;IAChC;IACAnB,SAAS,CAACc,YAAY,CAACK,UAAU,CAAC,KAAKM,SAAS,EAAE,wBAAwBN,UAAU,GAAG,EAAE,qBAAqB,CAAC;IAC/G,OAAOL,YAAY,CAACK,UAAU,CAAC;EACnC;EACA,OAAOA,UAAU;AACrB,CAAC;AAED,SAASD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}