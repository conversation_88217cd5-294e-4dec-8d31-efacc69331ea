import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTheme } from '../contexts/ThemeContext';
import { 
  Moon, 
  Sun, 
  Menu, 
  X, 
  Hotel,
  Calendar
} from 'lucide-react';
import { cn } from '../lib/utils';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const location = useLocation();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Rooms', path: '/rooms' },
    { name: 'Facilities', path: '/facilities' },
    { name: 'Gallery', path: '/gallery' },
    { name: 'Contact', path: '/contact' },
    { name: 'My Bookings', path: '/my-bookings' },
    { name: 'Dashboard', path: '/dashboard' },
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo */}
        <Link to="/" className="navbar-logo">
          <Hotel style={{ width: '2rem', height: '2rem', color: 'var(--primary-600)' }} />
          <span>SereneStay</span>
        </Link>

        {/* Desktop Navigation */}
        <div className="navbar-nav">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "navbar-link",
                isActive(item.path) && "active"
              )}
            >
              {item.name}
              {isActive(item.path) && (
                <motion.div
                  style={{
                    position: 'absolute',
                    bottom: '-1rem',
                    left: 0,
                    right: 0,
                    height: '2px',
                    backgroundColor: 'var(--primary-600)'
                  }}
                  layoutId="navbar-indicator"
                  initial={false}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}
            </Link>
          ))}
        </div>

        {/* Right side buttons */}
        <div className="navbar-actions">
          {/* Book Now Button */}
          <Link to="/rooms" className="btn btn-primary">
            <Calendar style={{ width: '1rem', height: '1rem', marginRight: '0.5rem' }} />
            Book Now
          </Link>

          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="theme-toggle"
            aria-label="Toggle theme"
          >
            {theme === 'light' ? (
              <Moon style={{ width: '1.25rem', height: '1.25rem', color: 'var(--text-secondary)' }} />
            ) : (
              <Sun style={{ width: '1.25rem', height: '1.25rem', color: 'var(--text-secondary)' }} />
            )}
          </button>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="theme-toggle"
            style={{ display: 'none' }}
            aria-label="Toggle menu"
            id="mobile-menu-btn"
          >
            {isOpen ? (
              <X style={{ width: '1.5rem', height: '1.5rem', color: 'var(--text-secondary)' }} />
            ) : (
              <Menu style={{ width: '1.5rem', height: '1.5rem', color: 'var(--text-secondary)' }} />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <motion.div
        initial={false}
        animate={{ height: isOpen ? 'auto' : 0 }}
        className={cn("mobile-menu", isOpen && "open")}
        style={{ overflow: 'hidden' }}
      >
        <div style={{ padding: '1rem 0', display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.path}
              onClick={() => setIsOpen(false)}
              className={cn(
                "mobile-menu-link",
                isActive(item.path) && "active"
              )}
            >
              {item.name}
            </Link>
          ))}
          <Link
            to="/rooms"
            onClick={() => setIsOpen(false)}
            className="btn btn-primary"
            style={{ marginTop: '1rem', textAlign: 'center' }}
          >
            <Calendar style={{ width: '1rem', height: '1rem', marginRight: '0.5rem' }} />
            Book Now
          </Link>
        </div>
      </motion.div>
    </nav>
  );
};

export default Navbar;
