{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 4v16\",\n  key: \"7j8fe9\"\n}], [\"path\", {\n  d: \"M6.029 4.285A2 2 0 0 0 3 6v12a2 2 0 0 0 3.029 1.715l9.997-5.998a2 2 0 0 0 .003-3.432z\",\n  key: \"zs4d6\"\n}]];\nconst SkipForward = createLucideIcon(\"skip-forward\", __iconNode);\nexport { __iconNode, SkipForward as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SkipForward", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\skip-forward.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21 4v16', key: '7j8fe9' }],\n  [\n    'path',\n    {\n      d: 'M6.029 4.285A2 2 0 0 0 3 6v12a2 2 0 0 0 3.029 1.715l9.997-5.998a2 2 0 0 0 .003-3.432z',\n      key: 'zs4d6',\n    },\n  ],\n];\n\n/**\n * @component @name SkipForward\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgNHYxNiIgLz4KICA8cGF0aCBkPSJNNi4wMjkgNC4yODVBMiAyIDAgMCAwIDMgNnYxMmEyIDIgMCAwIDAgMy4wMjkgMS43MTVsOS45OTctNS45OThhMiAyIDAgMCAwIC4wMDMtMy40MzJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/skip-forward\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SkipForward = createLucideIcon('skip-forward', __iconNode);\n\nexport default SkipForward;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EACP,CAEJ;AAaA,MAAMC,WAAA,GAAcC,gBAAA,CAAiB,gBAAgBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}