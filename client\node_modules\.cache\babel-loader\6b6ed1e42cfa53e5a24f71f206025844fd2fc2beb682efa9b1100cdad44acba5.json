{"ast": null, "code": "import { MotionGlobalConfig } from 'motion-utils';\nimport { isWillChangeMotionValue } from './is.mjs';\nfunction addValueToWillChange(visualElement, key) {\n  const willChange = visualElement.getValue(\"willChange\");\n  /**\n   * It could be that a user has set will<PERSON>hange to a regular MotionValue,\n   * in which case we can't add the value to it.\n   */\n  if (isWillChangeMotionValue(willChange)) {\n    return willChange.add(key);\n  } else if (!willChange && MotionGlobalConfig.WillChange) {\n    const newWillChange = new MotionGlobalConfig.WillChange(\"auto\");\n    visualElement.addValue(\"willChange\", newWillChange);\n    newWillChange.add(key);\n  }\n}\nexport { addValueToWillChange };", "map": {"version": 3, "names": ["MotionGlobalConfig", "isWillChangeMotionValue", "addValueToWillChange", "visualElement", "key", "<PERSON><PERSON><PERSON><PERSON>", "getValue", "add", "Will<PERSON><PERSON><PERSON>", "newWillChange", "addValue"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs"], "sourcesContent": ["import { MotionGlobalConfig } from 'motion-utils';\nimport { isWillChangeMotionValue } from './is.mjs';\n\nfunction addValueToWillChange(visualElement, key) {\n    const willChange = visualElement.getValue(\"willChange\");\n    /**\n     * It could be that a user has set will<PERSON>hange to a regular MotionValue,\n     * in which case we can't add the value to it.\n     */\n    if (isWillChangeMotionValue(willChange)) {\n        return willChange.add(key);\n    }\n    else if (!willChange && MotionGlobalConfig.WillChange) {\n        const newWillChange = new MotionGlobalConfig.WillChange(\"auto\");\n        visualElement.addValue(\"willChange\", newWillChange);\n        newWillChange.add(key);\n    }\n}\n\nexport { addValueToWillChange };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,cAAc;AACjD,SAASC,uBAAuB,QAAQ,UAAU;AAElD,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,GAAG,EAAE;EAC9C,MAAMC,UAAU,GAAGF,aAAa,CAACG,QAAQ,CAAC,YAAY,CAAC;EACvD;AACJ;AACA;AACA;EACI,IAAIL,uBAAuB,CAACI,UAAU,CAAC,EAAE;IACrC,OAAOA,UAAU,CAACE,GAAG,CAACH,GAAG,CAAC;EAC9B,CAAC,MACI,IAAI,CAACC,UAAU,IAAIL,kBAAkB,CAACQ,UAAU,EAAE;IACnD,MAAMC,aAAa,GAAG,IAAIT,kBAAkB,CAACQ,UAAU,CAAC,MAAM,CAAC;IAC/DL,aAAa,CAACO,QAAQ,CAAC,YAAY,EAAED,aAAa,CAAC;IACnDA,aAAa,CAACF,GAAG,CAACH,GAAG,CAAC;EAC1B;AACJ;AAEA,SAASF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}