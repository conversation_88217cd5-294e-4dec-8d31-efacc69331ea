import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Users,
  Bed,
  Maximize,
  Star,
  Wifi,
  Tv,
  Coffee,
  Car,
  Wind,
  Eye,
  Calendar
} from 'lucide-react';
import { rooms } from '../data/mockData';
import BookingModal from '../components/BookingModal';
import { cn } from '../lib/utils';

const Rooms = () => {
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);

  const getAmenityIcon = (amenity) => {
    const iconMap = {
      'Free Wi-Fi': Wifi,
      'Smart TV': Tv,
      'TV': Tv,
      'Mini Bar': Coffee,
      'Room Service': Car,
      'Air Conditioning': Wind,
      'City View': Eye,
      'Ocean View': Eye,
      'Garden View': Eye,
      'Panoramic View': Eye,
    };
    return iconMap[amenity] || Coffee;
  };

  const handleReserve = (room) => {
    setSelectedRoom(room);
    setIsBookingModalOpen(true);
  };

  const handleCloseBookingModal = () => {
    setIsBookingModalOpen(false);
    setSelectedRoom(null);
  };

  return (
    <div className="min-h-screen py-12" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>
            Our Rooms & Suites
          </h1>
          <p className="text-xl mx-auto" style={{ color: 'var(--text-secondary)', maxWidth: '48rem' }}>
            Choose from our carefully designed rooms and suites, each offering comfort,
            luxury, and modern amenities for an unforgettable stay.
          </p>
        </motion.div>

        {/* Rooms Grid */}
        <div className="grid lg:grid-cols-2 gap-8" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))', gap: '2rem' }}>
          {rooms.map((room, index) => (
            <motion.div
              key={room.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="room-card"
            >
              {/* Room Image */}
              <div className="room-image">
                <img
                  src={room.image}
                  alt={room.name}
                />
                <div className="room-rating">
                  <Star style={{ width: '1rem', height: '1rem', color: 'var(--yellow-400)', fill: 'currentColor' }} />
                  <span>{room.rating}</span>
                </div>
              </div>

              {/* Room Details */}
              <div className="room-content">
                <div className="room-header">
                  <div>
                    <h3 className="room-title">
                      {room.name}
                    </h3>
                    <p className="room-description">
                      {room.description}
                    </p>
                  </div>
                  <div className="room-price">
                    <div className="room-price-amount">
                      ${room.price}
                    </div>
                    <div className="room-price-period">
                      per night
                    </div>
                  </div>
                </div>

                {/* Room Info */}
                <div className="room-info">
                  <div className="room-info-item">
                    <Users className="room-info-icon" />
                    <div className="room-info-text">
                      {room.capacity} Guests
                    </div>
                  </div>
                  <div className="room-info-item">
                    <Bed className="room-info-icon" />
                    <div className="room-info-text">
                      {room.beds}
                    </div>
                  </div>
                  <div className="room-info-item">
                    <Maximize className="room-info-icon" />
                    <div className="room-info-text">
                      {room.size}
                    </div>
                  </div>
                </div>

                {/* Amenities */}
                <div className="room-amenities">
                  <h4 className="room-amenities-title">
                    Amenities
                  </h4>
                  <div className="amenities-list">
                    {room.amenities.slice(0, 6).map((amenity, idx) => {
                      const IconComponent = getAmenityIcon(amenity);
                      return (
                        <div key={idx} className="amenity-tag">
                          <IconComponent style={{ width: '0.75rem', height: '0.75rem' }} />
                          <span>{amenity}</span>
                        </div>
                      );
                    })}
                    {room.amenities.length > 6 && (
                      <div className="amenity-tag amenity-more">
                        +{room.amenities.length - 6} more
                      </div>
                    )}
                  </div>
                </div>

                {/* Reserve Button */}
                <button
                  onClick={() => handleReserve(room)}
                  className="btn btn-primary w-full"
                  style={{ width: '100%', justifyContent: 'center', gap: '0.5rem' }}
                >
                  <Calendar style={{ width: '1.25rem', height: '1.25rem' }} />
                  <span>Reserve Now</span>
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="card p-8 mt-16"
        >
          <h2 className="text-2xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>
            Need Help Choosing?
          </h2>
          <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
            Our team is here to help you find the perfect room for your stay.
            Contact us for personalized recommendations based on your preferences and needs.
          </p>
          <div className="flex flex-col gap-4" style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <button className="btn btn-primary">
              Call Us: +1 (555) 123-4567
            </button>
            <button className="btn btn-outline">
              Live Chat Support
            </button>
          </div>
        </motion.div>

        {/* Booking Modal */}
        <BookingModal
          isOpen={isBookingModalOpen}
          onClose={handleCloseBookingModal}
          room={selectedRoom}
        />
      </div>
    </div>
  );
};

export default Rooms;
