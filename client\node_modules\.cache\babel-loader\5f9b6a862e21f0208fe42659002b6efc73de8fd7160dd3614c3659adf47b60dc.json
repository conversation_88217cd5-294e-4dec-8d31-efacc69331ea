{"ast": null, "code": "import { calcLength } from './delta-calc.mjs';\nfunction isAxisDeltaZero(delta) {\n  return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n  return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction axisEquals(a, b) {\n  return a.min === b.min && a.max === b.max;\n}\nfunction boxEquals(a, b) {\n  return axisEquals(a.x, b.x) && axisEquals(a.y, b.y);\n}\nfunction axisEqualsRounded(a, b) {\n  return Math.round(a.min) === Math.round(b.min) && Math.round(a.max) === Math.round(b.max);\n}\nfunction boxEqualsRounded(a, b) {\n  return axisEqualsRounded(a.x, b.x) && axisEqualsRounded(a.y, b.y);\n}\nfunction aspectRatio(box) {\n  return calcLength(box.x) / calcLength(box.y);\n}\nfunction axisDeltaEquals(a, b) {\n  return a.translate === b.translate && a.scale === b.scale && a.originPoint === b.originPoint;\n}\nexport { aspectRatio, axisDeltaEquals, axisEquals, axisEqualsRounded, boxEquals, boxEqualsRounded, isDeltaZero };", "map": {"version": 3, "names": ["calcLength", "isAxisDeltaZero", "delta", "translate", "scale", "isDeltaZero", "x", "y", "axisEquals", "a", "b", "min", "max", "boxEquals", "axisEqualsRounded", "Math", "round", "boxEqualsRounded", "aspectRatio", "box", "axisDeltaEquals", "originPoint"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs"], "sourcesContent": ["import { calcLength } from './delta-calc.mjs';\n\nfunction isAxisDeltaZero(delta) {\n    return delta.translate === 0 && delta.scale === 1;\n}\nfunction isDeltaZero(delta) {\n    return isAxisDeltaZero(delta.x) && isAxisDeltaZero(delta.y);\n}\nfunction axisEquals(a, b) {\n    return a.min === b.min && a.max === b.max;\n}\nfunction boxEquals(a, b) {\n    return axisEquals(a.x, b.x) && axisEquals(a.y, b.y);\n}\nfunction axisEqualsRounded(a, b) {\n    return (Math.round(a.min) === Math.round(b.min) &&\n        Math.round(a.max) === Math.round(b.max));\n}\nfunction boxEqualsRounded(a, b) {\n    return axisEqualsRounded(a.x, b.x) && axisEqualsRounded(a.y, b.y);\n}\nfunction aspectRatio(box) {\n    return calcLength(box.x) / calcLength(box.y);\n}\nfunction axisDeltaEquals(a, b) {\n    return (a.translate === b.translate &&\n        a.scale === b.scale &&\n        a.originPoint === b.originPoint);\n}\n\nexport { aspectRatio, axisDeltaEquals, axisEquals, axisEqualsRounded, boxEquals, boxEqualsRounded, isDeltaZero };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB;AAE7C,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC5B,OAAOA,KAAK,CAACC,SAAS,KAAK,CAAC,IAAID,KAAK,CAACE,KAAK,KAAK,CAAC;AACrD;AACA,SAASC,WAAWA,CAACH,KAAK,EAAE;EACxB,OAAOD,eAAe,CAACC,KAAK,CAACI,CAAC,CAAC,IAAIL,eAAe,CAACC,KAAK,CAACK,CAAC,CAAC;AAC/D;AACA,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,CAACE,GAAG,KAAKD,CAAC,CAACC,GAAG,IAAIF,CAAC,CAACG,GAAG,KAAKF,CAAC,CAACE,GAAG;AAC7C;AACA,SAASC,SAASA,CAACJ,CAAC,EAAEC,CAAC,EAAE;EACrB,OAAOF,UAAU,CAACC,CAAC,CAACH,CAAC,EAAEI,CAAC,CAACJ,CAAC,CAAC,IAAIE,UAAU,CAACC,CAAC,CAACF,CAAC,EAAEG,CAAC,CAACH,CAAC,CAAC;AACvD;AACA,SAASO,iBAAiBA,CAACL,CAAC,EAAEC,CAAC,EAAE;EAC7B,OAAQK,IAAI,CAACC,KAAK,CAACP,CAAC,CAACE,GAAG,CAAC,KAAKI,IAAI,CAACC,KAAK,CAACN,CAAC,CAACC,GAAG,CAAC,IAC3CI,IAAI,CAACC,KAAK,CAACP,CAAC,CAACG,GAAG,CAAC,KAAKG,IAAI,CAACC,KAAK,CAACN,CAAC,CAACE,GAAG,CAAC;AAC/C;AACA,SAASK,gBAAgBA,CAACR,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOI,iBAAiB,CAACL,CAAC,CAACH,CAAC,EAAEI,CAAC,CAACJ,CAAC,CAAC,IAAIQ,iBAAiB,CAACL,CAAC,CAACF,CAAC,EAAEG,CAAC,CAACH,CAAC,CAAC;AACrE;AACA,SAASW,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOnB,UAAU,CAACmB,GAAG,CAACb,CAAC,CAAC,GAAGN,UAAU,CAACmB,GAAG,CAACZ,CAAC,CAAC;AAChD;AACA,SAASa,eAAeA,CAACX,CAAC,EAAEC,CAAC,EAAE;EAC3B,OAAQD,CAAC,CAACN,SAAS,KAAKO,CAAC,CAACP,SAAS,IAC/BM,CAAC,CAACL,KAAK,KAAKM,CAAC,CAACN,KAAK,IACnBK,CAAC,CAACY,WAAW,KAAKX,CAAC,CAACW,WAAW;AACvC;AAEA,SAASH,WAAW,EAAEE,eAAe,EAAEZ,UAAU,EAAEM,iBAAiB,EAAED,SAAS,EAAEI,gBAAgB,EAAEZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}