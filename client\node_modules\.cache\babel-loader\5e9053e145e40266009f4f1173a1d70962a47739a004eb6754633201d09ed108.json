{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 21V3h8\",\n  key: \"br2l0g\"\n}], [\"path\", {\n  d: \"M6 16h9\",\n  key: \"2py0wn\"\n}], [\"path\", {\n  d: \"M10 9.5h7\",\n  key: \"13dmhz\"\n}]];\nconst SwissFranc = createLucideIcon(\"swiss-franc\", __iconNode);\nexport { __iconNode, SwissFranc as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "SwissFranc", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\swiss-franc.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 21V3h8', key: 'br2l0g' }],\n  ['path', { d: 'M6 16h9', key: '2py0wn' }],\n  ['path', { d: 'M10 9.5h7', key: '13dmhz' }],\n];\n\n/**\n * @component @name SwissFranc\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjFWM2g4IiAvPgogIDxwYXRoIGQ9Ik02IDE2aDkiIC8+CiAgPHBhdGggZD0iTTEwIDkuNWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/swiss-franc\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SwissFranc = createLucideIcon('swiss-franc', __iconNode);\n\nexport default SwissFranc;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,EAC5C;AAaA,MAAMC,UAAA,GAAaC,gBAAA,CAAiB,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}