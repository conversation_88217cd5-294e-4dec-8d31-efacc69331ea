{"ast": null, "code": "import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\nconst numberValueTypes = {\n  // Border props\n  borderWidth: px,\n  borderTopWidth: px,\n  borderRightWidth: px,\n  borderBottomWidth: px,\n  borderLeftWidth: px,\n  borderRadius: px,\n  radius: px,\n  borderTopLeftRadius: px,\n  borderTopRightRadius: px,\n  borderBottomRightRadius: px,\n  borderBottomLeftRadius: px,\n  // Positioning props\n  width: px,\n  maxWidth: px,\n  height: px,\n  maxHeight: px,\n  top: px,\n  right: px,\n  bottom: px,\n  left: px,\n  // Spacing props\n  padding: px,\n  paddingTop: px,\n  paddingRight: px,\n  paddingBottom: px,\n  paddingLeft: px,\n  margin: px,\n  marginTop: px,\n  marginRight: px,\n  marginBottom: px,\n  marginLeft: px,\n  // Misc\n  backgroundPositionX: px,\n  backgroundPositionY: px,\n  ...transformValueTypes,\n  zIndex: int,\n  // SVG\n  fillOpacity: alpha,\n  strokeOpacity: alpha,\n  numOctaves: int\n};\nexport { numberValueTypes };", "map": {"version": 3, "names": ["int", "alpha", "px", "transformValueTypes", "numberValueTypes", "borderWidth", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "borderRadius", "radius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "top", "right", "bottom", "left", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "margin", "marginTop", "marginRight", "marginBottom", "marginLeft", "backgroundPositionX", "backgroundPositionY", "zIndex", "fillOpacity", "strokeOpacity", "numOctaves"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/value/types/maps/number.mjs"], "sourcesContent": ["import { int } from '../int.mjs';\nimport { alpha } from '../numbers/index.mjs';\nimport { px } from '../numbers/units.mjs';\nimport { transformValueTypes } from './transform.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    ...transformValueTypes,\n    zIndex: int,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,YAAY;AAChC,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,EAAE,QAAQ,sBAAsB;AACzC,SAASC,mBAAmB,QAAQ,iBAAiB;AAErD,MAAMC,gBAAgB,GAAG;EACrB;EACAC,WAAW,EAAEH,EAAE;EACfI,cAAc,EAAEJ,EAAE;EAClBK,gBAAgB,EAAEL,EAAE;EACpBM,iBAAiB,EAAEN,EAAE;EACrBO,eAAe,EAAEP,EAAE;EACnBQ,YAAY,EAAER,EAAE;EAChBS,MAAM,EAAET,EAAE;EACVU,mBAAmB,EAAEV,EAAE;EACvBW,oBAAoB,EAAEX,EAAE;EACxBY,uBAAuB,EAAEZ,EAAE;EAC3Ba,sBAAsB,EAAEb,EAAE;EAC1B;EACAc,KAAK,EAAEd,EAAE;EACTe,QAAQ,EAAEf,EAAE;EACZgB,MAAM,EAAEhB,EAAE;EACViB,SAAS,EAAEjB,EAAE;EACbkB,GAAG,EAAElB,EAAE;EACPmB,KAAK,EAAEnB,EAAE;EACToB,MAAM,EAAEpB,EAAE;EACVqB,IAAI,EAAErB,EAAE;EACR;EACAsB,OAAO,EAAEtB,EAAE;EACXuB,UAAU,EAAEvB,EAAE;EACdwB,YAAY,EAAExB,EAAE;EAChByB,aAAa,EAAEzB,EAAE;EACjB0B,WAAW,EAAE1B,EAAE;EACf2B,MAAM,EAAE3B,EAAE;EACV4B,SAAS,EAAE5B,EAAE;EACb6B,WAAW,EAAE7B,EAAE;EACf8B,YAAY,EAAE9B,EAAE;EAChB+B,UAAU,EAAE/B,EAAE;EACd;EACAgC,mBAAmB,EAAEhC,EAAE;EACvBiC,mBAAmB,EAAEjC,EAAE;EACvB,GAAGC,mBAAmB;EACtBiC,MAAM,EAAEpC,GAAG;EACX;EACAqC,WAAW,EAAEpC,KAAK;EAClBqC,aAAa,EAAErC,KAAK;EACpBsC,UAAU,EAAEvC;AAChB,CAAC;AAED,SAASI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}