{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"ellipse\", {\n  cx: \"12\",\n  cy: \"11\",\n  rx: \"3\",\n  ry: \"2\",\n  key: \"1b2qxu\"\n}], [\"ellipse\", {\n  cx: \"12\",\n  cy: \"12.5\",\n  rx: \"10\",\n  ry: \"8.5\",\n  key: \"h8emeu\"\n}]];\nconst Torus = createLucideIcon(\"torus\", __iconNode);\nexport { __iconNode, Torus as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "rx", "ry", "key", "<PERSON><PERSON>", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\torus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '11', rx: '3', ry: '2', key: '1b2qxu' }],\n  ['ellipse', { cx: '12', cy: '12.5', rx: '10', ry: '8.5', key: 'h8emeu' }],\n];\n\n/**\n * @component @name Torus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSIxMSIgcng9IjMiIHJ5PSIyIiAvPgogIDxlbGxpcHNlIGN4PSIxMiIgY3k9IjEyLjUiIHJ4PSIxMCIgcnk9IjguNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/torus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Torus = createLucideIcon('torus', __iconNode);\n\nexport default Torus;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,WAAW;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAKC,EAAA,EAAI;EAAKC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,WAAW;EAAEJ,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAQC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAOC,GAAA,EAAK;AAAA,CAAU,EAC1E;AAaA,MAAMC,KAAA,GAAQC,gBAAA,CAAiB,SAASP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}