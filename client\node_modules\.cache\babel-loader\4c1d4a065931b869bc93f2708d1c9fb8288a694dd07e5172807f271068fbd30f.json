{"ast": null, "code": "import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\nconst frameloopDriver = update => {\n  const passTimestamp = ({\n    timestamp\n  }) => update(timestamp);\n  return {\n    start: (keepAlive = true) => frame.update(passTimestamp, keepAlive),\n    stop: () => cancelFrame(passTimestamp),\n    /**\n     * If we're processing this frame we can use the\n     * framelocked timestamp to keep things in sync.\n     */\n    now: () => frameData.isProcessing ? frameData.timestamp : time.now()\n  };\n};\nexport { frameloopDriver };", "map": {"version": 3, "names": ["time", "frame", "cancelFrame", "frameData", "frameloopDriver", "update", "passTimestamp", "timestamp", "start", "keepAlive", "stop", "now", "isProcessing"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/animation/drivers/frame.mjs"], "sourcesContent": ["import { time } from '../../frameloop/sync-time.mjs';\nimport { frame, cancelFrame, frameData } from '../../frameloop/frame.mjs';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: (keepAlive = true) => frame.update(passTimestamp, keepAlive),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => (frameData.isProcessing ? frameData.timestamp : time.now()),\n    };\n};\n\nexport { frameloopDriver };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,+BAA+B;AACpD,SAASC,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,2BAA2B;AAEzE,MAAMC,eAAe,GAAIC,MAAM,IAAK;EAChC,MAAMC,aAAa,GAAGA,CAAC;IAAEC;EAAU,CAAC,KAAKF,MAAM,CAACE,SAAS,CAAC;EAC1D,OAAO;IACHC,KAAK,EAAEA,CAACC,SAAS,GAAG,IAAI,KAAKR,KAAK,CAACI,MAAM,CAACC,aAAa,EAAEG,SAAS,CAAC;IACnEC,IAAI,EAAEA,CAAA,KAAMR,WAAW,CAACI,aAAa,CAAC;IACtC;AACR;AACA;AACA;IACQK,GAAG,EAAEA,CAAA,KAAOR,SAAS,CAACS,YAAY,GAAGT,SAAS,CAACI,SAAS,GAAGP,IAAI,CAACW,GAAG,CAAC;EACxE,CAAC;AACL,CAAC;AAED,SAASP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}