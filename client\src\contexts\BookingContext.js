import React, { createContext, useContext, useReducer } from 'react';

const BookingContext = createContext();

export const useBooking = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

// Booking reducer
const bookingReducer = (state, action) => {
  switch (action.type) {
    case 'SET_SEARCH_CRITERIA':
      return {
        ...state,
        searchCriteria: { ...state.searchCriteria, ...action.payload }
      };
    case 'SET_SELECTED_ROOM':
      return {
        ...state,
        selectedRoom: action.payload
      };
    case 'SET_GUEST_INFO':
      return {
        ...state,
        guestInfo: { ...state.guestInfo, ...action.payload }
      };
    case 'SET_BOOKING_STEP':
      return {
        ...state,
        currentStep: action.payload
      };
    case 'ADD_BOOKING':
      return {
        ...state,
        bookings: [...state.bookings, action.payload],
        currentBooking: action.payload
      };
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload
      };
    case 'CLEAR_BOOKING':
      return {
        ...state,
        selectedRoom: null,
        guestInfo: {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          specialRequests: ''
        },
        currentStep: 1,
        currentBooking: null,
        error: null
      };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  searchCriteria: {
    checkIn: '',
    checkOut: '',
    guests: 1,
    roomType: ''
  },
  selectedRoom: null,
  guestInfo: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    specialRequests: ''
  },
  currentStep: 1, // 1: Search, 2: Select Room, 3: Guest Info, 4: Confirmation
  bookings: [],
  currentBooking: null,
  loading: false,
  error: null
};

export const BookingProvider = ({ children }) => {
  const [state, dispatch] = useReducer(bookingReducer, initialState);

  // Actions
  const setSearchCriteria = (criteria) => {
    dispatch({ type: 'SET_SEARCH_CRITERIA', payload: criteria });
  };

  const setSelectedRoom = (room) => {
    dispatch({ type: 'SET_SELECTED_ROOM', payload: room });
  };

  const setGuestInfo = (info) => {
    dispatch({ type: 'SET_GUEST_INFO', payload: info });
  };

  const setBookingStep = (step) => {
    dispatch({ type: 'SET_BOOKING_STEP', payload: step });
  };

  const calculateTotalPrice = () => {
    if (!state.selectedRoom || !state.searchCriteria.checkIn || !state.searchCriteria.checkOut) {
      return 0;
    }
    
    const checkIn = new Date(state.searchCriteria.checkIn);
    const checkOut = new Date(state.searchCriteria.checkOut);
    const nights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    
    return nights * state.selectedRoom.price;
  };

  const calculateNights = () => {
    if (!state.searchCriteria.checkIn || !state.searchCriteria.checkOut) {
      return 0;
    }
    
    const checkIn = new Date(state.searchCriteria.checkIn);
    const checkOut = new Date(state.searchCriteria.checkOut);
    return Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
  };

  const submitBooking = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const bookingData = {
        guestName: `${state.guestInfo.firstName} ${state.guestInfo.lastName}`,
        guestEmail: state.guestInfo.email,
        guestPhone: state.guestInfo.phone,
        roomId: state.selectedRoom.id,
        checkIn: state.searchCriteria.checkIn,
        checkOut: state.searchCriteria.checkOut,
        nights: calculateNights(),
        totalAmount: calculateTotalPrice(),
        specialRequests: state.guestInfo.specialRequests,
        guests: state.searchCriteria.guests
      };

      // Make API call to backend
      const response = await fetch('http://localhost:5000/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });

      const result = await response.json();

      if (result.success) {
        dispatch({ type: 'ADD_BOOKING', payload: result.data });
        dispatch({ type: 'SET_BOOKING_STEP', payload: 4 });
        return { success: true, booking: result.data };
      } else {
        throw new Error(result.message || 'Failed to create booking');
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: error.message });
      return { success: false, error: error.message };
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const clearBooking = () => {
    dispatch({ type: 'CLEAR_BOOKING' });
  };

  const value = {
    ...state,
    setSearchCriteria,
    setSelectedRoom,
    setGuestInfo,
    setBookingStep,
    calculateTotalPrice,
    calculateNights,
    submitBooking,
    clearBooking
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};
