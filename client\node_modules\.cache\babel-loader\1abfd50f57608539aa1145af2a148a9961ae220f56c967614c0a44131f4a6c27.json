{"ast": null, "code": "import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\nfunction createMixers(output, ease, customMixer) {\n  const mixers = [];\n  const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n  const numMixers = output.length - 1;\n  for (let i = 0; i < numMixers; i++) {\n    let mixer = mixerFactory(output[i], output[i + 1]);\n    if (ease) {\n      const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n      mixer = pipe(easingFunction, mixer);\n    }\n    mixers.push(mixer);\n  }\n  return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, {\n  clamp: isClamp = true,\n  ease,\n  mixer\n} = {}) {\n  const inputLength = input.length;\n  invariant(inputLength === output.length, \"Both input and output ranges must be the same length\", \"range-length\");\n  /**\n   * If we're only provided a single input, we can just make a function\n   * that returns the output.\n   */\n  if (inputLength === 1) return () => output[0];\n  if (inputLength === 2 && output[0] === output[1]) return () => output[1];\n  const isZeroDeltaRange = input[0] === input[1];\n  // If input runs highest -> lowest, reverse both arrays\n  if (input[0] > input[inputLength - 1]) {\n    input = [...input].reverse();\n    output = [...output].reverse();\n  }\n  const mixers = createMixers(output, ease, mixer);\n  const numMixers = mixers.length;\n  const interpolator = v => {\n    if (isZeroDeltaRange && v < input[0]) return output[0];\n    let i = 0;\n    if (numMixers > 1) {\n      for (; i < input.length - 2; i++) {\n        if (v < input[i + 1]) break;\n      }\n    }\n    const progressInRange = progress(input[i], input[i + 1], v);\n    return mixers[i](progressInRange);\n  };\n  return isClamp ? v => interpolator(clamp(input[0], input[inputLength - 1], v)) : interpolator;\n}\nexport { interpolate };", "map": {"version": 3, "names": ["invariant", "clamp", "MotionGlobalConfig", "noop", "pipe", "progress", "mix", "createMixers", "output", "ease", "customMixer", "mixers", "mixerFactory", "numMixers", "length", "i", "mixer", "easingFunction", "Array", "isArray", "push", "interpolate", "input", "isClamp", "inputLength", "isZeroDeltaRange", "reverse", "interpolator", "v", "progressInRange"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/utils/interpolate.mjs"], "sourcesContent": ["import { invariant, clamp, MotionGlobalConfig, noop, pipe, progress } from 'motion-utils';\nimport { mix } from './mix/index.mjs';\n\nfunction createMixers(output, ease, customMixer) {\n    const mixers = [];\n    const mixerFactory = customMixer || MotionGlobalConfig.mix || mix;\n    const numMixers = output.length - 1;\n    for (let i = 0; i < numMixers; i++) {\n        let mixer = mixerFactory(output[i], output[i + 1]);\n        if (ease) {\n            const easingFunction = Array.isArray(ease) ? ease[i] || noop : ease;\n            mixer = pipe(easingFunction, mixer);\n        }\n        mixers.push(mixer);\n    }\n    return mixers;\n}\n/**\n * Create a function that maps from a numerical input array to a generic output array.\n *\n * Accepts:\n *   - Numbers\n *   - Colors (hex, hsl, hsla, rgb, rgba)\n *   - Complex (combinations of one or more numbers or strings)\n *\n * ```jsx\n * const mixColor = interpolate([0, 1], ['#fff', '#000'])\n *\n * mixColor(0.5) // 'rgba(128, 128, 128, 1)'\n * ```\n *\n * TODO Revisit this approach once we've moved to data models for values,\n * probably not needed to pregenerate mixer functions.\n *\n * @public\n */\nfunction interpolate(input, output, { clamp: isClamp = true, ease, mixer } = {}) {\n    const inputLength = input.length;\n    invariant(inputLength === output.length, \"Both input and output ranges must be the same length\", \"range-length\");\n    /**\n     * If we're only provided a single input, we can just make a function\n     * that returns the output.\n     */\n    if (inputLength === 1)\n        return () => output[0];\n    if (inputLength === 2 && output[0] === output[1])\n        return () => output[1];\n    const isZeroDeltaRange = input[0] === input[1];\n    // If input runs highest -> lowest, reverse both arrays\n    if (input[0] > input[inputLength - 1]) {\n        input = [...input].reverse();\n        output = [...output].reverse();\n    }\n    const mixers = createMixers(output, ease, mixer);\n    const numMixers = mixers.length;\n    const interpolator = (v) => {\n        if (isZeroDeltaRange && v < input[0])\n            return output[0];\n        let i = 0;\n        if (numMixers > 1) {\n            for (; i < input.length - 2; i++) {\n                if (v < input[i + 1])\n                    break;\n            }\n        }\n        const progressInRange = progress(input[i], input[i + 1], v);\n        return mixers[i](progressInRange);\n    };\n    return isClamp\n        ? (v) => interpolator(clamp(input[0], input[inputLength - 1], v))\n        : interpolator;\n}\n\nexport { interpolate };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AACzF,SAASC,GAAG,QAAQ,iBAAiB;AAErC,SAASC,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE;EAC7C,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,YAAY,GAAGF,WAAW,IAAIR,kBAAkB,CAACI,GAAG,IAAIA,GAAG;EACjE,MAAMO,SAAS,GAAGL,MAAM,CAACM,MAAM,GAAG,CAAC;EACnC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;IAChC,IAAIC,KAAK,GAAGJ,YAAY,CAACJ,MAAM,CAACO,CAAC,CAAC,EAAEP,MAAM,CAACO,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,IAAIN,IAAI,EAAE;MACN,MAAMQ,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACV,IAAI,CAAC,GAAGA,IAAI,CAACM,CAAC,CAAC,IAAIZ,IAAI,GAAGM,IAAI;MACnEO,KAAK,GAAGZ,IAAI,CAACa,cAAc,EAAED,KAAK,CAAC;IACvC;IACAL,MAAM,CAACS,IAAI,CAACJ,KAAK,CAAC;EACtB;EACA,OAAOL,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,WAAWA,CAACC,KAAK,EAAEd,MAAM,EAAE;EAAEP,KAAK,EAAEsB,OAAO,GAAG,IAAI;EAAEd,IAAI;EAAEO;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC7E,MAAMQ,WAAW,GAAGF,KAAK,CAACR,MAAM;EAChCd,SAAS,CAACwB,WAAW,KAAKhB,MAAM,CAACM,MAAM,EAAE,sDAAsD,EAAE,cAAc,CAAC;EAChH;AACJ;AACA;AACA;EACI,IAAIU,WAAW,KAAK,CAAC,EACjB,OAAO,MAAMhB,MAAM,CAAC,CAAC,CAAC;EAC1B,IAAIgB,WAAW,KAAK,CAAC,IAAIhB,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,EAC5C,OAAO,MAAMA,MAAM,CAAC,CAAC,CAAC;EAC1B,MAAMiB,gBAAgB,GAAGH,KAAK,CAAC,CAAC,CAAC,KAAKA,KAAK,CAAC,CAAC,CAAC;EAC9C;EACA,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE;IACnCF,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC,CAACI,OAAO,CAAC,CAAC;IAC5BlB,MAAM,GAAG,CAAC,GAAGA,MAAM,CAAC,CAACkB,OAAO,CAAC,CAAC;EAClC;EACA,MAAMf,MAAM,GAAGJ,YAAY,CAACC,MAAM,EAAEC,IAAI,EAAEO,KAAK,CAAC;EAChD,MAAMH,SAAS,GAAGF,MAAM,CAACG,MAAM;EAC/B,MAAMa,YAAY,GAAIC,CAAC,IAAK;IACxB,IAAIH,gBAAgB,IAAIG,CAAC,GAAGN,KAAK,CAAC,CAAC,CAAC,EAChC,OAAOd,MAAM,CAAC,CAAC,CAAC;IACpB,IAAIO,CAAC,GAAG,CAAC;IACT,IAAIF,SAAS,GAAG,CAAC,EAAE;MACf,OAAOE,CAAC,GAAGO,KAAK,CAACR,MAAM,GAAG,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC9B,IAAIa,CAAC,GAAGN,KAAK,CAACP,CAAC,GAAG,CAAC,CAAC,EAChB;MACR;IACJ;IACA,MAAMc,eAAe,GAAGxB,QAAQ,CAACiB,KAAK,CAACP,CAAC,CAAC,EAAEO,KAAK,CAACP,CAAC,GAAG,CAAC,CAAC,EAAEa,CAAC,CAAC;IAC3D,OAAOjB,MAAM,CAACI,CAAC,CAAC,CAACc,eAAe,CAAC;EACrC,CAAC;EACD,OAAON,OAAO,GACPK,CAAC,IAAKD,YAAY,CAAC1B,KAAK,CAACqB,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAEI,CAAC,CAAC,CAAC,GAC/DD,YAAY;AACtB;AAEA,SAASN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}