{"ast": null, "code": "import { clsx } from \"clsx\";\nexport function cn(...inputs) {\n  return clsx(inputs);\n}", "map": {"version": 3, "names": ["clsx", "cn", "inputs"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\n\nexport function cn(...inputs) {\n  return clsx(inputs);\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,MAAM;AAE3B,OAAO,SAASC,EAAEA,CAAC,GAAGC,MAAM,EAAE;EAC5B,OAAOF,IAAI,CAACE,MAAM,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}