{"ast": null, "code": "import { removeItem } from 'motion-utils';\nimport { microtask } from '../frameloop/microtask.mjs';\nimport { startViewAnimation } from './start.mjs';\nlet builders = [];\nlet current = null;\nfunction next() {\n  current = null;\n  const [nextBuilder] = builders;\n  if (nextBuilder) start(nextBuilder);\n}\nfunction start(builder) {\n  removeItem(builders, builder);\n  current = builder;\n  startViewAnimation(builder).then(animation => {\n    builder.notifyReady(animation);\n    animation.finished.finally(next);\n  });\n}\nfunction processQueue() {\n  /**\n   * Iterate backwards over the builders array. We can ignore the\n   * \"wait\" animations. If we have an interrupting animation in the\n   * queue then we need to batch all preceeding animations into it.\n   * Currently this only batches the update functions but will also\n   * need to batch the targets.\n   */\n  for (let i = builders.length - 1; i >= 0; i--) {\n    const builder = builders[i];\n    const {\n      interrupt\n    } = builder.options;\n    if (interrupt === \"immediate\") {\n      const batchedUpdates = builders.slice(0, i + 1).map(b => b.update);\n      const remaining = builders.slice(i + 1);\n      builder.update = () => {\n        batchedUpdates.forEach(update => update());\n      };\n      // Put the current builder at the front, followed by any \"wait\" builders\n      builders = [builder, ...remaining];\n      break;\n    }\n  }\n  if (!current || builders[0]?.options.interrupt === \"immediate\") {\n    next();\n  }\n}\nfunction addToQueue(builder) {\n  builders.push(builder);\n  microtask.render(processQueue);\n}\nexport { addToQueue };", "map": {"version": 3, "names": ["removeItem", "microtask", "startViewAnimation", "builders", "current", "next", "nextBuilder", "start", "builder", "then", "animation", "notifyReady", "finished", "finally", "processQueue", "i", "length", "interrupt", "options", "batchedUpdates", "slice", "map", "b", "update", "remaining", "for<PERSON>ach", "addToQueue", "push", "render"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/view/queue.mjs"], "sourcesContent": ["import { removeItem } from 'motion-utils';\nimport { microtask } from '../frameloop/microtask.mjs';\nimport { startViewAnimation } from './start.mjs';\n\nlet builders = [];\nlet current = null;\nfunction next() {\n    current = null;\n    const [nextBuilder] = builders;\n    if (nextBuilder)\n        start(nextBuilder);\n}\nfunction start(builder) {\n    removeItem(builders, builder);\n    current = builder;\n    startViewAnimation(builder).then((animation) => {\n        builder.notifyReady(animation);\n        animation.finished.finally(next);\n    });\n}\nfunction processQueue() {\n    /**\n     * Iterate backwards over the builders array. We can ignore the\n     * \"wait\" animations. If we have an interrupting animation in the\n     * queue then we need to batch all preceeding animations into it.\n     * Currently this only batches the update functions but will also\n     * need to batch the targets.\n     */\n    for (let i = builders.length - 1; i >= 0; i--) {\n        const builder = builders[i];\n        const { interrupt } = builder.options;\n        if (interrupt === \"immediate\") {\n            const batchedUpdates = builders.slice(0, i + 1).map((b) => b.update);\n            const remaining = builders.slice(i + 1);\n            builder.update = () => {\n                batchedUpdates.forEach((update) => update());\n            };\n            // Put the current builder at the front, followed by any \"wait\" builders\n            builders = [builder, ...remaining];\n            break;\n        }\n    }\n    if (!current || builders[0]?.options.interrupt === \"immediate\") {\n        next();\n    }\n}\nfunction addToQueue(builder) {\n    builders.push(builder);\n    microtask.render(processQueue);\n}\n\nexport { addToQueue };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,SAAS,QAAQ,4BAA4B;AACtD,SAASC,kBAAkB,QAAQ,aAAa;AAEhD,IAAIC,QAAQ,GAAG,EAAE;AACjB,IAAIC,OAAO,GAAG,IAAI;AAClB,SAASC,IAAIA,CAAA,EAAG;EACZD,OAAO,GAAG,IAAI;EACd,MAAM,CAACE,WAAW,CAAC,GAAGH,QAAQ;EAC9B,IAAIG,WAAW,EACXC,KAAK,CAACD,WAAW,CAAC;AAC1B;AACA,SAASC,KAAKA,CAACC,OAAO,EAAE;EACpBR,UAAU,CAACG,QAAQ,EAAEK,OAAO,CAAC;EAC7BJ,OAAO,GAAGI,OAAO;EACjBN,kBAAkB,CAACM,OAAO,CAAC,CAACC,IAAI,CAAEC,SAAS,IAAK;IAC5CF,OAAO,CAACG,WAAW,CAACD,SAAS,CAAC;IAC9BA,SAAS,CAACE,QAAQ,CAACC,OAAO,CAACR,IAAI,CAAC;EACpC,CAAC,CAAC;AACN;AACA,SAASS,YAAYA,CAAA,EAAG;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAGZ,QAAQ,CAACa,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,MAAMP,OAAO,GAAGL,QAAQ,CAACY,CAAC,CAAC;IAC3B,MAAM;MAAEE;IAAU,CAAC,GAAGT,OAAO,CAACU,OAAO;IACrC,IAAID,SAAS,KAAK,WAAW,EAAE;MAC3B,MAAME,cAAc,GAAGhB,QAAQ,CAACiB,KAAK,CAAC,CAAC,EAAEL,CAAC,GAAG,CAAC,CAAC,CAACM,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC;MACpE,MAAMC,SAAS,GAAGrB,QAAQ,CAACiB,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC;MACvCP,OAAO,CAACe,MAAM,GAAG,MAAM;QACnBJ,cAAc,CAACM,OAAO,CAAEF,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;MAChD,CAAC;MACD;MACApB,QAAQ,GAAG,CAACK,OAAO,EAAE,GAAGgB,SAAS,CAAC;MAClC;IACJ;EACJ;EACA,IAAI,CAACpB,OAAO,IAAID,QAAQ,CAAC,CAAC,CAAC,EAAEe,OAAO,CAACD,SAAS,KAAK,WAAW,EAAE;IAC5DZ,IAAI,CAAC,CAAC;EACV;AACJ;AACA,SAASqB,UAAUA,CAAClB,OAAO,EAAE;EACzBL,QAAQ,CAACwB,IAAI,CAACnB,OAAO,CAAC;EACtBP,SAAS,CAAC2B,MAAM,CAACd,YAAY,CAAC;AAClC;AAEA,SAASY,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}