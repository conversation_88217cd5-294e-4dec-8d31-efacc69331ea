{"ast": null, "code": "import { resolveElements, getValueTransition, getAnimationMap, animationMapKey, getComputedStyle, fillWildcards, applyPxDefaults, NativeAnimation } from 'motion-dom';\nimport { invariant, secondsToMilliseconds } from 'motion-utils';\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n  const elements = resolveElements(elementOrSelector, scope);\n  const numElements = elements.length;\n  invariant(Boolean(numElements), \"No valid elements provided.\", \"no-valid-elements\");\n  /**\n   * WAAPI doesn't support interrupting animations.\n   *\n   * Therefore, starting animations requires a three-step process:\n   * 1. Stop existing animations (write styles to DOM)\n   * 2. Resolve keyframes (read styles from DOM)\n   * 3. Create new animations (write styles to DOM)\n   *\n   * The hybrid `animate()` function uses AsyncAnimation to resolve\n   * keyframes before creating new animations, which removes style\n   * thrashing. Here, we have much stricter filesize constraints.\n   * Therefore we do this in a synchronous way that ensures that\n   * at least within `animate()` calls there is no style thrashing.\n   *\n   * In the motion-native-animate-mini-interrupt benchmark this\n   * was 80% faster than a single loop.\n   */\n  const animationDefinitions = [];\n  /**\n   * Step 1: Build options and stop existing animations (write)\n   */\n  for (let i = 0; i < numElements; i++) {\n    const element = elements[i];\n    const elementTransition = {\n      ...options\n    };\n    /**\n     * Resolve stagger function if provided.\n     */\n    if (typeof elementTransition.delay === \"function\") {\n      elementTransition.delay = elementTransition.delay(i, numElements);\n    }\n    for (const valueName in keyframes) {\n      let valueKeyframes = keyframes[valueName];\n      if (!Array.isArray(valueKeyframes)) {\n        valueKeyframes = [valueKeyframes];\n      }\n      const valueOptions = {\n        ...getValueTransition(elementTransition, valueName)\n      };\n      valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n      valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n      /**\n       * If there's an existing animation playing on this element then stop it\n       * before creating a new one.\n       */\n      const map = getAnimationMap(element);\n      const key = animationMapKey(valueName, valueOptions.pseudoElement || \"\");\n      const currentAnimation = map.get(key);\n      currentAnimation && currentAnimation.stop();\n      animationDefinitions.push({\n        map,\n        key,\n        unresolvedKeyframes: valueKeyframes,\n        options: {\n          ...valueOptions,\n          element,\n          name: valueName,\n          allowFlatten: !elementTransition.type && !elementTransition.ease\n        }\n      });\n    }\n  }\n  /**\n   * Step 2: Resolve keyframes (read)\n   */\n  for (let i = 0; i < animationDefinitions.length; i++) {\n    const {\n      unresolvedKeyframes,\n      options: animationOptions\n    } = animationDefinitions[i];\n    const {\n      element,\n      name,\n      pseudoElement\n    } = animationOptions;\n    if (!pseudoElement && unresolvedKeyframes[0] === null) {\n      unresolvedKeyframes[0] = getComputedStyle(element, name);\n    }\n    fillWildcards(unresolvedKeyframes);\n    applyPxDefaults(unresolvedKeyframes, name);\n    /**\n     * If we only have one keyframe, explicitly read the initial keyframe\n     * from the computed style. This is to ensure consistency with WAAPI behaviour\n     * for restarting animations, for instance .play() after finish, when it\n     * has one vs two keyframes.\n     */\n    if (!pseudoElement && unresolvedKeyframes.length < 2) {\n      unresolvedKeyframes.unshift(getComputedStyle(element, name));\n    }\n    animationOptions.keyframes = unresolvedKeyframes;\n  }\n  /**\n   * Step 3: Create new animations (write)\n   */\n  const animations = [];\n  for (let i = 0; i < animationDefinitions.length; i++) {\n    const {\n      map,\n      key,\n      options: animationOptions\n    } = animationDefinitions[i];\n    const animation = new NativeAnimation(animationOptions);\n    map.set(key, animation);\n    animation.finished.finally(() => map.delete(key));\n    animations.push(animation);\n  }\n  return animations;\n}\nexport { animateElements };", "map": {"version": 3, "names": ["resolveElements", "getValueTransition", "getAnimationMap", "animationMapKey", "getComputedStyle", "fillWildcards", "applyPxDefaults", "NativeAnimation", "invariant", "secondsToMilliseconds", "animateElements", "elementOrSelector", "keyframes", "options", "scope", "elements", "numElements", "length", "Boolean", "animationDefinitions", "i", "element", "elementTransition", "delay", "valueName", "valueKeyframes", "Array", "isArray", "valueOptions", "duration", "map", "key", "pseudoElement", "currentAnimation", "get", "stop", "push", "unresolvedKeyframes", "name", "allowFlatten", "type", "ease", "animationOptions", "unshift", "animations", "animation", "set", "finished", "finally", "delete"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/animation/animators/waapi/animate-elements.mjs"], "sourcesContent": ["import { resolveElements, getValueTransition, getAnimationMap, animationMapKey, getComputedStyle, fillWildcards, applyPxDefaults, NativeAnimation } from 'motion-dom';\nimport { invariant, secondsToMilliseconds } from 'motion-utils';\n\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n    const elements = resolveElements(elementOrSelector, scope);\n    const numElements = elements.length;\n    invariant(Boolean(numElements), \"No valid elements provided.\", \"no-valid-elements\");\n    /**\n     * WAAPI doesn't support interrupting animations.\n     *\n     * Therefore, starting animations requires a three-step process:\n     * 1. Stop existing animations (write styles to DOM)\n     * 2. Resolve keyframes (read styles from DOM)\n     * 3. Create new animations (write styles to DOM)\n     *\n     * The hybrid `animate()` function uses AsyncAnimation to resolve\n     * keyframes before creating new animations, which removes style\n     * thrashing. Here, we have much stricter filesize constraints.\n     * Therefore we do this in a synchronous way that ensures that\n     * at least within `animate()` calls there is no style thrashing.\n     *\n     * In the motion-native-animate-mini-interrupt benchmark this\n     * was 80% faster than a single loop.\n     */\n    const animationDefinitions = [];\n    /**\n     * Step 1: Build options and stop existing animations (write)\n     */\n    for (let i = 0; i < numElements; i++) {\n        const element = elements[i];\n        const elementTransition = { ...options };\n        /**\n         * Resolve stagger function if provided.\n         */\n        if (typeof elementTransition.delay === \"function\") {\n            elementTransition.delay = elementTransition.delay(i, numElements);\n        }\n        for (const valueName in keyframes) {\n            let valueKeyframes = keyframes[valueName];\n            if (!Array.isArray(valueKeyframes)) {\n                valueKeyframes = [valueKeyframes];\n            }\n            const valueOptions = {\n                ...getValueTransition(elementTransition, valueName),\n            };\n            valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n            valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n            /**\n             * If there's an existing animation playing on this element then stop it\n             * before creating a new one.\n             */\n            const map = getAnimationMap(element);\n            const key = animationMapKey(valueName, valueOptions.pseudoElement || \"\");\n            const currentAnimation = map.get(key);\n            currentAnimation && currentAnimation.stop();\n            animationDefinitions.push({\n                map,\n                key,\n                unresolvedKeyframes: valueKeyframes,\n                options: {\n                    ...valueOptions,\n                    element,\n                    name: valueName,\n                    allowFlatten: !elementTransition.type && !elementTransition.ease,\n                },\n            });\n        }\n    }\n    /**\n     * Step 2: Resolve keyframes (read)\n     */\n    for (let i = 0; i < animationDefinitions.length; i++) {\n        const { unresolvedKeyframes, options: animationOptions } = animationDefinitions[i];\n        const { element, name, pseudoElement } = animationOptions;\n        if (!pseudoElement && unresolvedKeyframes[0] === null) {\n            unresolvedKeyframes[0] = getComputedStyle(element, name);\n        }\n        fillWildcards(unresolvedKeyframes);\n        applyPxDefaults(unresolvedKeyframes, name);\n        /**\n         * If we only have one keyframe, explicitly read the initial keyframe\n         * from the computed style. This is to ensure consistency with WAAPI behaviour\n         * for restarting animations, for instance .play() after finish, when it\n         * has one vs two keyframes.\n         */\n        if (!pseudoElement && unresolvedKeyframes.length < 2) {\n            unresolvedKeyframes.unshift(getComputedStyle(element, name));\n        }\n        animationOptions.keyframes = unresolvedKeyframes;\n    }\n    /**\n     * Step 3: Create new animations (write)\n     */\n    const animations = [];\n    for (let i = 0; i < animationDefinitions.length; i++) {\n        const { map, key, options: animationOptions } = animationDefinitions[i];\n        const animation = new NativeAnimation(animationOptions);\n        map.set(key, animation);\n        animation.finished.finally(() => map.delete(key));\n        animations.push(animation);\n    }\n    return animations;\n}\n\nexport { animateElements };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,eAAe,QAAQ,YAAY;AACrK,SAASC,SAAS,EAAEC,qBAAqB,QAAQ,cAAc;AAE/D,SAASC,eAAeA,CAACC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACnE,MAAMC,QAAQ,GAAGf,eAAe,CAACW,iBAAiB,EAAEG,KAAK,CAAC;EAC1D,MAAME,WAAW,GAAGD,QAAQ,CAACE,MAAM;EACnCT,SAAS,CAACU,OAAO,CAACF,WAAW,CAAC,EAAE,6BAA6B,EAAE,mBAAmB,CAAC;EACnF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMG,oBAAoB,GAAG,EAAE;EAC/B;AACJ;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAE,EAAE;IAClC,MAAMC,OAAO,GAAGN,QAAQ,CAACK,CAAC,CAAC;IAC3B,MAAME,iBAAiB,GAAG;MAAE,GAAGT;IAAQ,CAAC;IACxC;AACR;AACA;IACQ,IAAI,OAAOS,iBAAiB,CAACC,KAAK,KAAK,UAAU,EAAE;MAC/CD,iBAAiB,CAACC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAACH,CAAC,EAAEJ,WAAW,CAAC;IACrE;IACA,KAAK,MAAMQ,SAAS,IAAIZ,SAAS,EAAE;MAC/B,IAAIa,cAAc,GAAGb,SAAS,CAACY,SAAS,CAAC;MACzC,IAAI,CAACE,KAAK,CAACC,OAAO,CAACF,cAAc,CAAC,EAAE;QAChCA,cAAc,GAAG,CAACA,cAAc,CAAC;MACrC;MACA,MAAMG,YAAY,GAAG;QACjB,GAAG3B,kBAAkB,CAACqB,iBAAiB,EAAEE,SAAS;MACtD,CAAC;MACDI,YAAY,CAACC,QAAQ,KAAKD,YAAY,CAACC,QAAQ,GAAGpB,qBAAqB,CAACmB,YAAY,CAACC,QAAQ,CAAC,CAAC;MAC/FD,YAAY,CAACL,KAAK,KAAKK,YAAY,CAACL,KAAK,GAAGd,qBAAqB,CAACmB,YAAY,CAACL,KAAK,CAAC,CAAC;MACtF;AACZ;AACA;AACA;MACY,MAAMO,GAAG,GAAG5B,eAAe,CAACmB,OAAO,CAAC;MACpC,MAAMU,GAAG,GAAG5B,eAAe,CAACqB,SAAS,EAAEI,YAAY,CAACI,aAAa,IAAI,EAAE,CAAC;MACxE,MAAMC,gBAAgB,GAAGH,GAAG,CAACI,GAAG,CAACH,GAAG,CAAC;MACrCE,gBAAgB,IAAIA,gBAAgB,CAACE,IAAI,CAAC,CAAC;MAC3ChB,oBAAoB,CAACiB,IAAI,CAAC;QACtBN,GAAG;QACHC,GAAG;QACHM,mBAAmB,EAAEZ,cAAc;QACnCZ,OAAO,EAAE;UACL,GAAGe,YAAY;UACfP,OAAO;UACPiB,IAAI,EAAEd,SAAS;UACfe,YAAY,EAAE,CAACjB,iBAAiB,CAACkB,IAAI,IAAI,CAAClB,iBAAiB,CAACmB;QAChE;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;EACI,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,oBAAoB,CAACF,MAAM,EAAEG,CAAC,EAAE,EAAE;IAClD,MAAM;MAAEiB,mBAAmB;MAAExB,OAAO,EAAE6B;IAAiB,CAAC,GAAGvB,oBAAoB,CAACC,CAAC,CAAC;IAClF,MAAM;MAAEC,OAAO;MAAEiB,IAAI;MAAEN;IAAc,CAAC,GAAGU,gBAAgB;IACzD,IAAI,CAACV,aAAa,IAAIK,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACnDA,mBAAmB,CAAC,CAAC,CAAC,GAAGjC,gBAAgB,CAACiB,OAAO,EAAEiB,IAAI,CAAC;IAC5D;IACAjC,aAAa,CAACgC,mBAAmB,CAAC;IAClC/B,eAAe,CAAC+B,mBAAmB,EAAEC,IAAI,CAAC;IAC1C;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACN,aAAa,IAAIK,mBAAmB,CAACpB,MAAM,GAAG,CAAC,EAAE;MAClDoB,mBAAmB,CAACM,OAAO,CAACvC,gBAAgB,CAACiB,OAAO,EAAEiB,IAAI,CAAC,CAAC;IAChE;IACAI,gBAAgB,CAAC9B,SAAS,GAAGyB,mBAAmB;EACpD;EACA;AACJ;AACA;EACI,MAAMO,UAAU,GAAG,EAAE;EACrB,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,oBAAoB,CAACF,MAAM,EAAEG,CAAC,EAAE,EAAE;IAClD,MAAM;MAAEU,GAAG;MAAEC,GAAG;MAAElB,OAAO,EAAE6B;IAAiB,CAAC,GAAGvB,oBAAoB,CAACC,CAAC,CAAC;IACvE,MAAMyB,SAAS,GAAG,IAAItC,eAAe,CAACmC,gBAAgB,CAAC;IACvDZ,GAAG,CAACgB,GAAG,CAACf,GAAG,EAAEc,SAAS,CAAC;IACvBA,SAAS,CAACE,QAAQ,CAACC,OAAO,CAAC,MAAMlB,GAAG,CAACmB,MAAM,CAAClB,GAAG,CAAC,CAAC;IACjDa,UAAU,CAACR,IAAI,CAACS,SAAS,CAAC;EAC9B;EACA,OAAOD,UAAU;AACrB;AAEA,SAASlC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}