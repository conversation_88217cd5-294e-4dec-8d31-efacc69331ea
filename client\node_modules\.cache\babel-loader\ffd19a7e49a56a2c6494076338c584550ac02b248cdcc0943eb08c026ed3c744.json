{"ast": null, "code": "import { fillWildcards } from './utils/fill-wildcards.mjs';\nimport { removeNonTranslationalTransform } from './utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nlet isForced = false;\nfunction measureAllKeyframes() {\n  if (anyNeedsMeasurement) {\n    const resolversToMeasure = Array.from(toResolve).filter(resolver => resolver.needsMeasurement);\n    const elementsToMeasure = new Set(resolversToMeasure.map(resolver => resolver.element));\n    const transformsToRestore = new Map();\n    /**\n     * Write pass\n     * If we're measuring elements we want to remove bounding box-changing transforms.\n     */\n    elementsToMeasure.forEach(element => {\n      const removedTransforms = removeNonTranslationalTransform(element);\n      if (!removedTransforms.length) return;\n      transformsToRestore.set(element, removedTransforms);\n      element.render();\n    });\n    // Read\n    resolversToMeasure.forEach(resolver => resolver.measureInitialState());\n    // Write\n    elementsToMeasure.forEach(element => {\n      element.render();\n      const restore = transformsToRestore.get(element);\n      if (restore) {\n        restore.forEach(([key, value]) => {\n          element.getValue(key)?.set(value);\n        });\n      }\n    });\n    // Read\n    resolversToMeasure.forEach(resolver => resolver.measureEndState());\n    // Write\n    resolversToMeasure.forEach(resolver => {\n      if (resolver.suspendedScrollY !== undefined) {\n        window.scrollTo(0, resolver.suspendedScrollY);\n      }\n    });\n  }\n  anyNeedsMeasurement = false;\n  isScheduled = false;\n  toResolve.forEach(resolver => resolver.complete(isForced));\n  toResolve.clear();\n}\nfunction readAllKeyframes() {\n  toResolve.forEach(resolver => {\n    resolver.readKeyframes();\n    if (resolver.needsMeasurement) {\n      anyNeedsMeasurement = true;\n    }\n  });\n}\nfunction flushKeyframeResolvers() {\n  isForced = true;\n  readAllKeyframes();\n  measureAllKeyframes();\n  isForced = false;\n}\nclass KeyframeResolver {\n  constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n    this.state = \"pending\";\n    /**\n     * Track whether this resolver is async. If it is, it'll be added to the\n     * resolver queue and flushed in the next frame. Resolvers that aren't going\n     * to trigger read/write thrashing don't need to be async.\n     */\n    this.isAsync = false;\n    /**\n     * Track whether this resolver needs to perform a measurement\n     * to resolve its keyframes.\n     */\n    this.needsMeasurement = false;\n    this.unresolvedKeyframes = [...unresolvedKeyframes];\n    this.onComplete = onComplete;\n    this.name = name;\n    this.motionValue = motionValue;\n    this.element = element;\n    this.isAsync = isAsync;\n  }\n  scheduleResolve() {\n    this.state = \"scheduled\";\n    if (this.isAsync) {\n      toResolve.add(this);\n      if (!isScheduled) {\n        isScheduled = true;\n        frame.read(readAllKeyframes);\n        frame.resolveKeyframes(measureAllKeyframes);\n      }\n    } else {\n      this.readKeyframes();\n      this.complete();\n    }\n  }\n  readKeyframes() {\n    const {\n      unresolvedKeyframes,\n      name,\n      element,\n      motionValue\n    } = this;\n    // If initial keyframe is null we need to read it from the DOM\n    if (unresolvedKeyframes[0] === null) {\n      const currentValue = motionValue?.get();\n      // TODO: This doesn't work if the final keyframe is a wildcard\n      const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n      if (currentValue !== undefined) {\n        unresolvedKeyframes[0] = currentValue;\n      } else if (element && name) {\n        const valueAsRead = element.readValue(name, finalKeyframe);\n        if (valueAsRead !== undefined && valueAsRead !== null) {\n          unresolvedKeyframes[0] = valueAsRead;\n        }\n      }\n      if (unresolvedKeyframes[0] === undefined) {\n        unresolvedKeyframes[0] = finalKeyframe;\n      }\n      if (motionValue && currentValue === undefined) {\n        motionValue.set(unresolvedKeyframes[0]);\n      }\n    }\n    fillWildcards(unresolvedKeyframes);\n  }\n  setFinalKeyframe() {}\n  measureInitialState() {}\n  renderEndStyles() {}\n  measureEndState() {}\n  complete(isForcedComplete = false) {\n    this.state = \"complete\";\n    this.onComplete(this.unresolvedKeyframes, this.finalKeyframe, isForcedComplete);\n    toResolve.delete(this);\n  }\n  cancel() {\n    if (this.state === \"scheduled\") {\n      toResolve.delete(this);\n      this.state = \"pending\";\n    }\n  }\n  resume() {\n    if (this.state === \"pending\") this.scheduleResolve();\n  }\n}\nexport { KeyframeResolver, flushKeyframeResolvers };", "map": {"version": 3, "names": ["fillWildcards", "removeNonTranslationalTransform", "frame", "toResolve", "Set", "isScheduled", "anyNeedsMeasurement", "isForced", "measureAllKeyframes", "resolversToMeasure", "Array", "from", "filter", "resolver", "needsMeasurement", "elementsToMeasure", "map", "element", "transformsToRestore", "Map", "for<PERSON>ach", "removedTransforms", "length", "set", "render", "measureInitialState", "restore", "get", "key", "value", "getValue", "measureEndState", "suspendedScrollY", "undefined", "window", "scrollTo", "complete", "clear", "readAllKeyframes", "readKeyframes", "flushKeyframeResolvers", "KeyframeResolver", "constructor", "unresolvedKeyframes", "onComplete", "name", "motionValue", "isAsync", "state", "scheduleResolve", "add", "read", "resolveKeyframes", "currentValue", "finalKeyframe", "valueAsRead", "readValue", "setFinalKeyframe", "renderEndStyles", "isForcedComplete", "delete", "cancel", "resume"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs"], "sourcesContent": ["import { fillWildcards } from './utils/fill-wildcards.mjs';\nimport { removeNonTranslationalTransform } from './utils/unit-conversion.mjs';\nimport { frame } from '../../frameloop/frame.mjs';\n\nconst toResolve = new Set();\nlet isScheduled = false;\nlet anyNeedsMeasurement = false;\nlet isForced = false;\nfunction measureAllKeyframes() {\n    if (anyNeedsMeasurement) {\n        const resolversToMeasure = Array.from(toResolve).filter((resolver) => resolver.needsMeasurement);\n        const elementsToMeasure = new Set(resolversToMeasure.map((resolver) => resolver.element));\n        const transformsToRestore = new Map();\n        /**\n         * Write pass\n         * If we're measuring elements we want to remove bounding box-changing transforms.\n         */\n        elementsToMeasure.forEach((element) => {\n            const removedTransforms = removeNonTranslationalTransform(element);\n            if (!removedTransforms.length)\n                return;\n            transformsToRestore.set(element, removedTransforms);\n            element.render();\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureInitialState());\n        // Write\n        elementsToMeasure.forEach((element) => {\n            element.render();\n            const restore = transformsToRestore.get(element);\n            if (restore) {\n                restore.forEach(([key, value]) => {\n                    element.getValue(key)?.set(value);\n                });\n            }\n        });\n        // Read\n        resolversToMeasure.forEach((resolver) => resolver.measureEndState());\n        // Write\n        resolversToMeasure.forEach((resolver) => {\n            if (resolver.suspendedScrollY !== undefined) {\n                window.scrollTo(0, resolver.suspendedScrollY);\n            }\n        });\n    }\n    anyNeedsMeasurement = false;\n    isScheduled = false;\n    toResolve.forEach((resolver) => resolver.complete(isForced));\n    toResolve.clear();\n}\nfunction readAllKeyframes() {\n    toResolve.forEach((resolver) => {\n        resolver.readKeyframes();\n        if (resolver.needsMeasurement) {\n            anyNeedsMeasurement = true;\n        }\n    });\n}\nfunction flushKeyframeResolvers() {\n    isForced = true;\n    readAllKeyframes();\n    measureAllKeyframes();\n    isForced = false;\n}\nclass KeyframeResolver {\n    constructor(unresolvedKeyframes, onComplete, name, motionValue, element, isAsync = false) {\n        this.state = \"pending\";\n        /**\n         * Track whether this resolver is async. If it is, it'll be added to the\n         * resolver queue and flushed in the next frame. Resolvers that aren't going\n         * to trigger read/write thrashing don't need to be async.\n         */\n        this.isAsync = false;\n        /**\n         * Track whether this resolver needs to perform a measurement\n         * to resolve its keyframes.\n         */\n        this.needsMeasurement = false;\n        this.unresolvedKeyframes = [...unresolvedKeyframes];\n        this.onComplete = onComplete;\n        this.name = name;\n        this.motionValue = motionValue;\n        this.element = element;\n        this.isAsync = isAsync;\n    }\n    scheduleResolve() {\n        this.state = \"scheduled\";\n        if (this.isAsync) {\n            toResolve.add(this);\n            if (!isScheduled) {\n                isScheduled = true;\n                frame.read(readAllKeyframes);\n                frame.resolveKeyframes(measureAllKeyframes);\n            }\n        }\n        else {\n            this.readKeyframes();\n            this.complete();\n        }\n    }\n    readKeyframes() {\n        const { unresolvedKeyframes, name, element, motionValue } = this;\n        // If initial keyframe is null we need to read it from the DOM\n        if (unresolvedKeyframes[0] === null) {\n            const currentValue = motionValue?.get();\n            // TODO: This doesn't work if the final keyframe is a wildcard\n            const finalKeyframe = unresolvedKeyframes[unresolvedKeyframes.length - 1];\n            if (currentValue !== undefined) {\n                unresolvedKeyframes[0] = currentValue;\n            }\n            else if (element && name) {\n                const valueAsRead = element.readValue(name, finalKeyframe);\n                if (valueAsRead !== undefined && valueAsRead !== null) {\n                    unresolvedKeyframes[0] = valueAsRead;\n                }\n            }\n            if (unresolvedKeyframes[0] === undefined) {\n                unresolvedKeyframes[0] = finalKeyframe;\n            }\n            if (motionValue && currentValue === undefined) {\n                motionValue.set(unresolvedKeyframes[0]);\n            }\n        }\n        fillWildcards(unresolvedKeyframes);\n    }\n    setFinalKeyframe() { }\n    measureInitialState() { }\n    renderEndStyles() { }\n    measureEndState() { }\n    complete(isForcedComplete = false) {\n        this.state = \"complete\";\n        this.onComplete(this.unresolvedKeyframes, this.finalKeyframe, isForcedComplete);\n        toResolve.delete(this);\n    }\n    cancel() {\n        if (this.state === \"scheduled\") {\n            toResolve.delete(this);\n            this.state = \"pending\";\n        }\n    }\n    resume() {\n        if (this.state === \"pending\")\n            this.scheduleResolve();\n    }\n}\n\nexport { KeyframeResolver, flushKeyframeResolvers };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,4BAA4B;AAC1D,SAASC,+BAA+B,QAAQ,6BAA6B;AAC7E,SAASC,KAAK,QAAQ,2BAA2B;AAEjD,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,IAAIC,WAAW,GAAG,KAAK;AACvB,IAAIC,mBAAmB,GAAG,KAAK;AAC/B,IAAIC,QAAQ,GAAG,KAAK;AACpB,SAASC,mBAAmBA,CAAA,EAAG;EAC3B,IAAIF,mBAAmB,EAAE;IACrB,MAAMG,kBAAkB,GAAGC,KAAK,CAACC,IAAI,CAACR,SAAS,CAAC,CAACS,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,gBAAgB,CAAC;IAChG,MAAMC,iBAAiB,GAAG,IAAIX,GAAG,CAACK,kBAAkB,CAACO,GAAG,CAAEH,QAAQ,IAAKA,QAAQ,CAACI,OAAO,CAAC,CAAC;IACzF,MAAMC,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACrC;AACR;AACA;AACA;IACQJ,iBAAiB,CAACK,OAAO,CAAEH,OAAO,IAAK;MACnC,MAAMI,iBAAiB,GAAGpB,+BAA+B,CAACgB,OAAO,CAAC;MAClE,IAAI,CAACI,iBAAiB,CAACC,MAAM,EACzB;MACJJ,mBAAmB,CAACK,GAAG,CAACN,OAAO,EAAEI,iBAAiB,CAAC;MACnDJ,OAAO,CAACO,MAAM,CAAC,CAAC;IACpB,CAAC,CAAC;IACF;IACAf,kBAAkB,CAACW,OAAO,CAAEP,QAAQ,IAAKA,QAAQ,CAACY,mBAAmB,CAAC,CAAC,CAAC;IACxE;IACAV,iBAAiB,CAACK,OAAO,CAAEH,OAAO,IAAK;MACnCA,OAAO,CAACO,MAAM,CAAC,CAAC;MAChB,MAAME,OAAO,GAAGR,mBAAmB,CAACS,GAAG,CAACV,OAAO,CAAC;MAChD,IAAIS,OAAO,EAAE;QACTA,OAAO,CAACN,OAAO,CAAC,CAAC,CAACQ,GAAG,EAAEC,KAAK,CAAC,KAAK;UAC9BZ,OAAO,CAACa,QAAQ,CAACF,GAAG,CAAC,EAAEL,GAAG,CAACM,KAAK,CAAC;QACrC,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;IACF;IACApB,kBAAkB,CAACW,OAAO,CAAEP,QAAQ,IAAKA,QAAQ,CAACkB,eAAe,CAAC,CAAC,CAAC;IACpE;IACAtB,kBAAkB,CAACW,OAAO,CAAEP,QAAQ,IAAK;MACrC,IAAIA,QAAQ,CAACmB,gBAAgB,KAAKC,SAAS,EAAE;QACzCC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAEtB,QAAQ,CAACmB,gBAAgB,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACA1B,mBAAmB,GAAG,KAAK;EAC3BD,WAAW,GAAG,KAAK;EACnBF,SAAS,CAACiB,OAAO,CAAEP,QAAQ,IAAKA,QAAQ,CAACuB,QAAQ,CAAC7B,QAAQ,CAAC,CAAC;EAC5DJ,SAAS,CAACkC,KAAK,CAAC,CAAC;AACrB;AACA,SAASC,gBAAgBA,CAAA,EAAG;EACxBnC,SAAS,CAACiB,OAAO,CAAEP,QAAQ,IAAK;IAC5BA,QAAQ,CAAC0B,aAAa,CAAC,CAAC;IACxB,IAAI1B,QAAQ,CAACC,gBAAgB,EAAE;MAC3BR,mBAAmB,GAAG,IAAI;IAC9B;EACJ,CAAC,CAAC;AACN;AACA,SAASkC,sBAAsBA,CAAA,EAAG;EAC9BjC,QAAQ,GAAG,IAAI;EACf+B,gBAAgB,CAAC,CAAC;EAClB9B,mBAAmB,CAAC,CAAC;EACrBD,QAAQ,GAAG,KAAK;AACpB;AACA,MAAMkC,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,mBAAmB,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAE7B,OAAO,EAAE8B,OAAO,GAAG,KAAK,EAAE;IACtF,IAAI,CAACC,KAAK,GAAG,SAAS;IACtB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACD,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACjC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC6B,mBAAmB,GAAG,CAAC,GAAGA,mBAAmB,CAAC;IACnD,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC7B,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC8B,OAAO,GAAGA,OAAO;EAC1B;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,CAACD,KAAK,GAAG,WAAW;IACxB,IAAI,IAAI,CAACD,OAAO,EAAE;MACd5C,SAAS,CAAC+C,GAAG,CAAC,IAAI,CAAC;MACnB,IAAI,CAAC7C,WAAW,EAAE;QACdA,WAAW,GAAG,IAAI;QAClBH,KAAK,CAACiD,IAAI,CAACb,gBAAgB,CAAC;QAC5BpC,KAAK,CAACkD,gBAAgB,CAAC5C,mBAAmB,CAAC;MAC/C;IACJ,CAAC,MACI;MACD,IAAI,CAAC+B,aAAa,CAAC,CAAC;MACpB,IAAI,CAACH,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAG,aAAaA,CAAA,EAAG;IACZ,MAAM;MAAEI,mBAAmB;MAAEE,IAAI;MAAE5B,OAAO;MAAE6B;IAAY,CAAC,GAAG,IAAI;IAChE;IACA,IAAIH,mBAAmB,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MACjC,MAAMU,YAAY,GAAGP,WAAW,EAAEnB,GAAG,CAAC,CAAC;MACvC;MACA,MAAM2B,aAAa,GAAGX,mBAAmB,CAACA,mBAAmB,CAACrB,MAAM,GAAG,CAAC,CAAC;MACzE,IAAI+B,YAAY,KAAKpB,SAAS,EAAE;QAC5BU,mBAAmB,CAAC,CAAC,CAAC,GAAGU,YAAY;MACzC,CAAC,MACI,IAAIpC,OAAO,IAAI4B,IAAI,EAAE;QACtB,MAAMU,WAAW,GAAGtC,OAAO,CAACuC,SAAS,CAACX,IAAI,EAAES,aAAa,CAAC;QAC1D,IAAIC,WAAW,KAAKtB,SAAS,IAAIsB,WAAW,KAAK,IAAI,EAAE;UACnDZ,mBAAmB,CAAC,CAAC,CAAC,GAAGY,WAAW;QACxC;MACJ;MACA,IAAIZ,mBAAmB,CAAC,CAAC,CAAC,KAAKV,SAAS,EAAE;QACtCU,mBAAmB,CAAC,CAAC,CAAC,GAAGW,aAAa;MAC1C;MACA,IAAIR,WAAW,IAAIO,YAAY,KAAKpB,SAAS,EAAE;QAC3Ca,WAAW,CAACvB,GAAG,CAACoB,mBAAmB,CAAC,CAAC,CAAC,CAAC;MAC3C;IACJ;IACA3C,aAAa,CAAC2C,mBAAmB,CAAC;EACtC;EACAc,gBAAgBA,CAAA,EAAG,CAAE;EACrBhC,mBAAmBA,CAAA,EAAG,CAAE;EACxBiC,eAAeA,CAAA,EAAG,CAAE;EACpB3B,eAAeA,CAAA,EAAG,CAAE;EACpBK,QAAQA,CAACuB,gBAAgB,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACX,KAAK,GAAG,UAAU;IACvB,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACD,mBAAmB,EAAE,IAAI,CAACW,aAAa,EAAEK,gBAAgB,CAAC;IAC/ExD,SAAS,CAACyD,MAAM,CAAC,IAAI,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACb,KAAK,KAAK,WAAW,EAAE;MAC5B7C,SAAS,CAACyD,MAAM,CAAC,IAAI,CAAC;MACtB,IAAI,CAACZ,KAAK,GAAG,SAAS;IAC1B;EACJ;EACAc,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACd,KAAK,KAAK,SAAS,EACxB,IAAI,CAACC,eAAe,CAAC,CAAC;EAC9B;AACJ;AAEA,SAASR,gBAAgB,EAAED,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}