{"name": "reduce-css-calc", "version": "2.1.8", "description": "Reduce CSS calc() function to the maximum", "keywords": ["css", "calculation", "calc"], "main": "dist/index.js", "files": ["dist"], "scripts": {"prepublish": "npm run build && del-cli dist/__tests__", "build": "del-cli dist && cross-env BABEL_ENV=publish babel src --out-dir dist && jison parser.jison -o dist/parser.js", "pretest": "eslint src && npm run build", "test": "ava dist/__tests__/", "release": "npmpub"}, "author": "<PERSON><PERSON>", "license": "MIT", "repository": "https://github.com/MoOx/reduce-css-calc.git", "devDependencies": {"ava": "^0.18.2", "babel-cli": "^6.18.0", "babel-core": "^6.21.0", "babel-eslint": "^7.1.1", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-env": "^1.6.0", "babel-register": "^6.18.0", "cross-env": "^3.1.4", "del-cli": "^0.2.1", "eslint": "^3.12.2", "eslint-config-i-am-meticulous": "^6.0.1", "eslint-plugin-babel": "^4.0.0", "eslint-plugin-import": "^2.2.0", "jison-gho": "^0.6.1-216", "npmpub": "^5.0.0"}, "dependencies": {"css-unit-converter": "^1.1.1", "postcss-value-parser": "^3.3.0"}, "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "eslint-config-i-am-meticulous"}}