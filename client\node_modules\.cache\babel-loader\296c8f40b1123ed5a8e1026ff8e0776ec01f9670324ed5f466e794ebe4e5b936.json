{"ast": null, "code": "let pendingRules = {};\nlet style = null;\nconst css = {\n  set: (selector, values) => {\n    pendingRules[selector] = values;\n  },\n  commit: () => {\n    if (!style) {\n      style = document.createElement(\"style\");\n      style.id = \"motion-view\";\n    }\n    let cssText = \"\";\n    for (const selector in pendingRules) {\n      const rule = pendingRules[selector];\n      cssText += `${selector} {\\n`;\n      for (const [property, value] of Object.entries(rule)) {\n        cssText += `  ${property}: ${value};\\n`;\n      }\n      cssText += \"}\\n\";\n    }\n    style.textContent = cssText;\n    document.head.appendChild(style);\n    pendingRules = {};\n  },\n  remove: () => {\n    if (style && style.parentElement) {\n      style.parentElement.removeChild(style);\n    }\n  }\n};\nexport { css };", "map": {"version": 3, "names": ["pendingRules", "style", "css", "set", "selector", "values", "commit", "document", "createElement", "id", "cssText", "rule", "property", "value", "Object", "entries", "textContent", "head", "append<PERSON><PERSON><PERSON>", "remove", "parentElement", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/view/utils/css.mjs"], "sourcesContent": ["let pendingRules = {};\nlet style = null;\nconst css = {\n    set: (selector, values) => {\n        pendingRules[selector] = values;\n    },\n    commit: () => {\n        if (!style) {\n            style = document.createElement(\"style\");\n            style.id = \"motion-view\";\n        }\n        let cssText = \"\";\n        for (const selector in pendingRules) {\n            const rule = pendingRules[selector];\n            cssText += `${selector} {\\n`;\n            for (const [property, value] of Object.entries(rule)) {\n                cssText += `  ${property}: ${value};\\n`;\n            }\n            cssText += \"}\\n\";\n        }\n        style.textContent = cssText;\n        document.head.appendChild(style);\n        pendingRules = {};\n    },\n    remove: () => {\n        if (style && style.parentElement) {\n            style.parentElement.removeChild(style);\n        }\n    },\n};\n\nexport { css };\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG,CAAC,CAAC;AACrB,IAAIC,KAAK,GAAG,IAAI;AAChB,MAAMC,GAAG,GAAG;EACRC,GAAG,EAAEA,CAACC,QAAQ,EAAEC,MAAM,KAAK;IACvBL,YAAY,CAACI,QAAQ,CAAC,GAAGC,MAAM;EACnC,CAAC;EACDC,MAAM,EAAEA,CAAA,KAAM;IACV,IAAI,CAACL,KAAK,EAAE;MACRA,KAAK,GAAGM,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACvCP,KAAK,CAACQ,EAAE,GAAG,aAAa;IAC5B;IACA,IAAIC,OAAO,GAAG,EAAE;IAChB,KAAK,MAAMN,QAAQ,IAAIJ,YAAY,EAAE;MACjC,MAAMW,IAAI,GAAGX,YAAY,CAACI,QAAQ,CAAC;MACnCM,OAAO,IAAI,GAAGN,QAAQ,MAAM;MAC5B,KAAK,MAAM,CAACQ,QAAQ,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;QAClDD,OAAO,IAAI,KAAKE,QAAQ,KAAKC,KAAK,KAAK;MAC3C;MACAH,OAAO,IAAI,KAAK;IACpB;IACAT,KAAK,CAACe,WAAW,GAAGN,OAAO;IAC3BH,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACjB,KAAK,CAAC;IAChCD,YAAY,GAAG,CAAC,CAAC;EACrB,CAAC;EACDmB,MAAM,EAAEA,CAAA,KAAM;IACV,IAAIlB,KAAK,IAAIA,KAAK,CAACmB,aAAa,EAAE;MAC9BnB,KAAK,CAACmB,aAAa,CAACC,WAAW,CAACpB,KAAK,CAAC;IAC1C;EACJ;AACJ,CAAC;AAED,SAASC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}