{"ast": null, "code": "import { collectMotionValues, motionValue } from './index.mjs';\nimport { subscribeValue } from './subscribe-value.mjs';\n\n/**\n * Create a `MotionValue` that transforms the output of other `MotionValue`s by\n * passing their latest values through a transform function.\n *\n * Whenever a `MotionValue` referred to in the provided function is updated,\n * it will be re-evaluated.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction transformValue(transform) {\n  const collectedValues = [];\n  /**\n   * Open session of collectMotionValues. Any MotionValue that calls get()\n   * inside transform will be saved into this array.\n   */\n  collectMotionValues.current = collectedValues;\n  const initialValue = transform();\n  collectMotionValues.current = undefined;\n  const value = motionValue(initialValue);\n  subscribeValue(collectedValues, value, transform);\n  return value;\n}\nexport { transformValue };", "map": {"version": 3, "names": ["collectMotionValues", "motionValue", "subscribeValue", "transformValue", "transform", "collected<PERSON><PERSON><PERSON>", "current", "initialValue", "undefined", "value"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/value/transform-value.mjs"], "sourcesContent": ["import { collectMotionValues, motionValue } from './index.mjs';\nimport { subscribeValue } from './subscribe-value.mjs';\n\n/**\n * Create a `MotionValue` that transforms the output of other `MotionValue`s by\n * passing their latest values through a transform function.\n *\n * Whenever a `MotionValue` referred to in the provided function is updated,\n * it will be re-evaluated.\n *\n * ```jsx\n * const x = motionValue(0)\n * const y = transformValue(() => x.get() * 2) // double x\n * ```\n *\n * @param transformer - A transform function. This function must be pure with no side-effects or conditional statements.\n * @returns `MotionValue`\n *\n * @public\n */\nfunction transformValue(transform) {\n    const collectedValues = [];\n    /**\n     * Open session of collectMotionValues. Any MotionValue that calls get()\n     * inside transform will be saved into this array.\n     */\n    collectMotionValues.current = collectedValues;\n    const initialValue = transform();\n    collectMotionValues.current = undefined;\n    const value = motionValue(initialValue);\n    subscribeValue(collectedValues, value, transform);\n    return value;\n}\n\nexport { transformValue };\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,WAAW,QAAQ,aAAa;AAC9D,SAASC,cAAc,QAAQ,uBAAuB;;AAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,SAAS,EAAE;EAC/B,MAAMC,eAAe,GAAG,EAAE;EAC1B;AACJ;AACA;AACA;EACIL,mBAAmB,CAACM,OAAO,GAAGD,eAAe;EAC7C,MAAME,YAAY,GAAGH,SAAS,CAAC,CAAC;EAChCJ,mBAAmB,CAACM,OAAO,GAAGE,SAAS;EACvC,MAAMC,KAAK,GAAGR,WAAW,CAACM,YAAY,CAAC;EACvCL,cAAc,CAACG,eAAe,EAAEI,KAAK,EAAEL,SAAS,CAAC;EACjD,OAAOK,KAAK;AAChB;AAEA,SAASN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}