{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 9a5 5 0 0 1 .95 2.293\",\n  key: \"1fgyg8\"\n}], [\"path\", {\n  d: \"M19.364 5.636a9 9 0 0 1 1.889 9.96\",\n  key: \"l3zxae\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"m7 7-.587.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298V11\",\n  key: \"1gbwow\"\n}], [\"path\", {\n  d: \"M9.828 4.172A.686.686 0 0 1 11 4.657v.686\",\n  key: \"s2je0y\"\n}]];\nconst VolumeOff = createLucideIcon(\"volume-off\", __iconNode);\nexport { __iconNode, VolumeOff as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "VolumeOff", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\volume-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 9a5 5 0 0 1 .95 2.293', key: '1fgyg8' }],\n  ['path', { d: 'M19.364 5.636a9 9 0 0 1 1.889 9.96', key: 'l3zxae' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n  [\n    'path',\n    {\n      d: 'm7 7-.587.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298V11',\n      key: '1gbwow',\n    },\n  ],\n  ['path', { d: 'M9.828 4.172A.686.686 0 0 1 11 4.657v.686', key: 's2je0y' }],\n];\n\n/**\n * @component @name VolumeOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgOWE1IDUgMCAwIDEgLjk1IDIuMjkzIiAvPgogIDxwYXRoIGQ9Ik0xOS4zNjQgNS42MzZhOSA5IDAgMCAxIDEuODg5IDkuOTYiIC8+CiAgPHBhdGggZD0ibTIgMiAyMCAyMCIgLz4KICA8cGF0aCBkPSJtNyA3LS41ODcuNTg3QTEuNCAxLjQgMCAwIDEgNS40MTYgOEgzYTEgMSAwIDAgMC0xIDF2NmExIDEgMCAwIDAgMSAxaDIuNDE2YTEuNCAxLjQgMCAwIDEgLjk5Ny40MTNsMy4zODMgMy4zODRBLjcwNS43MDUgMCAwIDAgMTEgMTkuMjk4VjExIiAvPgogIDxwYXRoIGQ9Ik05LjgyOCA0LjE3MkEuNjg2LjY4NiAwIDAgMSAxMSA0LjY1N3YuNjg2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/volume-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst VolumeOff = createLucideIcon('volume-off', __iconNode);\n\nexport default VolumeOff;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA6BC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAsCC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAA,EAAG;EACHC,GAAA,EAAK;AAAA,EAET,EACA,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA6CC,GAAA,EAAK;AAAA,CAAU,EAC5E;AAaA,MAAMC,SAAA,GAAYC,gBAAA,CAAiB,cAAcJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}