{"ast": null, "code": "import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nconst useSVGVisualState = /*@__PURE__*/makeUseVisualState({\n  scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n  createRenderState: createSvgRenderState\n});\nexport { useSVGVisualState };", "map": {"version": 3, "names": ["makeUseVisualState", "createSvgRenderState", "scrapeMotionValuesFromProps", "useSVGVisualState", "createRenderState"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/framer-motion/dist/es/render/svg/use-svg-visual-state.mjs"], "sourcesContent": ["import { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nconst useSVGVisualState = /*@__PURE__*/ makeUseVisualState({\n    scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n    createRenderState: createSvgRenderState,\n});\n\nexport { useSVGVisualState };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,MAAMC,iBAAiB,GAAG,aAAcH,kBAAkB,CAAC;EACvDE,2BAA2B,EAAEA,2BAA2B;EACxDE,iBAAiB,EAAEH;AACvB,CAAC,CAAC;AAEF,SAASE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}