{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 10V2\",\n  key: \"16sf7g\"\n}], [\"path\", {\n  d: \"m4.93 10.93 1.41 1.41\",\n  key: \"2a7f42\"\n}], [\"path\", {\n  d: \"M2 18h2\",\n  key: \"j10viu\"\n}], [\"path\", {\n  d: \"M20 18h2\",\n  key: \"wocana\"\n}], [\"path\", {\n  d: \"m19.07 10.93-1.41 1.41\",\n  key: \"15zs5n\"\n}], [\"path\", {\n  d: \"M22 22H2\",\n  key: \"19qnx5\"\n}], [\"path\", {\n  d: \"m16 6-4 4-4-4\",\n  key: \"6wukr\"\n}], [\"path\", {\n  d: \"M16 18a4 4 0 0 0-8 0\",\n  key: \"1lzouq\"\n}]];\nconst Sunset = createLucideIcon(\"sunset\", __iconNode);\nexport { __iconNode, Sunset as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Sunset", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\sunset.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 10V2', key: '16sf7g' }],\n  ['path', { d: 'm4.93 10.93 1.41 1.41', key: '2a7f42' }],\n  ['path', { d: 'M2 18h2', key: 'j10viu' }],\n  ['path', { d: 'M20 18h2', key: 'wocana' }],\n  ['path', { d: 'm19.07 10.93-1.41 1.41', key: '15zs5n' }],\n  ['path', { d: 'M22 22H2', key: '19qnx5' }],\n  ['path', { d: 'm16 6-4 4-4-4', key: '6wukr' }],\n  ['path', { d: 'M16 18a4 4 0 0 0-8 0', key: '1lzouq' }],\n];\n\n/**\n * @component @name Sunset\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTBWMiIgLz4KICA8cGF0aCBkPSJtNC45MyAxMC45MyAxLjQxIDEuNDEiIC8+CiAgPHBhdGggZD0iTTIgMThoMiIgLz4KICA8cGF0aCBkPSJNMjAgMThoMiIgLz4KICA8cGF0aCBkPSJtMTkuMDcgMTAuOTMtMS40MSAxLjQxIiAvPgogIDxwYXRoIGQ9Ik0yMiAyMkgyIiAvPgogIDxwYXRoIGQ9Im0xNiA2LTQgNC00LTQiIC8+CiAgPHBhdGggZD0iTTE2IDE4YTQgNCAwIDAgMC04IDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/sunset\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Sunset = createLucideIcon('sunset', __iconNode);\n\nexport default Sunset;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAyBC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAWC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA0BC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAiBC,GAAA,EAAK;AAAA,CAAS,GAC7C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAwBC,GAAA,EAAK;AAAA,CAAU,EACvD;AAaA,MAAMC,MAAA,GAASC,gBAAA,CAAiB,UAAUJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}