{"ast": null, "code": "function makeAnimationInstant(options) {\n  options.duration = 0;\n  options.type === \"keyframes\";\n}\nexport { makeAnimationInstant };", "map": {"version": 3, "names": ["makeAnimationInstant", "options", "duration", "type"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/animation/utils/make-animation-instant.mjs"], "sourcesContent": ["function makeAnimationInstant(options) {\n    options.duration = 0;\n    options.type === \"keyframes\";\n}\n\nexport { makeAnimationInstant };\n"], "mappings": "AAAA,SAASA,oBAAoBA,CAACC,OAAO,EAAE;EACnCA,OAAO,CAACC,QAAQ,GAAG,CAAC;EACpBD,OAAO,CAACE,IAAI,KAAK,WAAW;AAChC;AAEA,SAASH,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}