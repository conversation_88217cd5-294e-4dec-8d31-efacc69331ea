{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m15 11-1 9\",\n  key: \"5wnq3a\"\n}], [\"path\", {\n  d: \"m19 11-4-7\",\n  key: \"cnml18\"\n}], [\"path\", {\n  d: \"M2 11h20\",\n  key: \"3eubbj\"\n}], [\"path\", {\n  d: \"m3.5 11 1.6 7.4a2 2 0 0 0 2 1.6h9.8a2 2 0 0 0 2-1.6l1.7-7.4\",\n  key: \"yiazzp\"\n}], [\"path\", {\n  d: \"M4.5 15.5h15\",\n  key: \"13mye1\"\n}], [\"path\", {\n  d: \"m5 11 4-7\",\n  key: \"116ra9\"\n}], [\"path\", {\n  d: \"m9 11 1 9\",\n  key: \"1ojof7\"\n}]];\nconst ShoppingBasket = createLucideIcon(\"shopping-basket\", __iconNode);\nexport { __iconNode, ShoppingBasket as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ShoppingBasket", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\shopping-basket.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm15 11-1 9', key: '5wnq3a' }],\n  ['path', { d: 'm19 11-4-7', key: 'cnml18' }],\n  ['path', { d: 'M2 11h20', key: '3eubbj' }],\n  ['path', { d: 'm3.5 11 1.6 7.4a2 2 0 0 0 2 1.6h9.8a2 2 0 0 0 2-1.6l1.7-7.4', key: 'yiazzp' }],\n  ['path', { d: 'M4.5 15.5h15', key: '13mye1' }],\n  ['path', { d: 'm5 11 4-7', key: '116ra9' }],\n  ['path', { d: 'm9 11 1 9', key: '1ojof7' }],\n];\n\n/**\n * @component @name ShoppingBasket\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTEtMSA5IiAvPgogIDxwYXRoIGQ9Im0xOSAxMS00LTciIC8+CiAgPHBhdGggZD0iTTIgMTFoMjAiIC8+CiAgPHBhdGggZD0ibTMuNSAxMSAxLjYgNy40YTIgMiAwIDAgMCAyIDEuNmg5LjhhMiAyIDAgMCAwIDItMS42bDEuNy03LjQiIC8+CiAgPHBhdGggZD0iTTQuNSAxNS41aDE1IiAvPgogIDxwYXRoIGQ9Im01IDExIDQtNyIgLz4KICA8cGF0aCBkPSJtOSAxMSAxIDkiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/shopping-basket\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ShoppingBasket = createLucideIcon('shopping-basket', __iconNode);\n\nexport default ShoppingBasket;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAYC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+DC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAgBC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,EAC5C;AAaA,MAAMC,cAAA,GAAiBC,gBAAA,CAAiB,mBAAmBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}