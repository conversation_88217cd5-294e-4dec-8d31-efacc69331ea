{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M8 3.1V7a4 4 0 0 0 8 0V3.1\",\n  key: \"1v71zp\"\n}], [\"path\", {\n  d: \"m9 15-1-1\",\n  key: \"1yrq24\"\n}], [\"path\", {\n  d: \"m15 15 1-1\",\n  key: \"1t0d6s\"\n}], [\"path\", {\n  d: \"M9 19c-2.8 0-5-2.2-5-5v-4a8 8 0 0 1 16 0v4c0 2.8-2.2 5-5 5Z\",\n  key: \"1p0hjs\"\n}], [\"path\", {\n  d: \"m8 19-2 3\",\n  key: \"13i0xs\"\n}], [\"path\", {\n  d: \"m16 19 2 3\",\n  key: \"xo31yx\"\n}]];\nconst TrainFront = createLucideIcon(\"train-front\", __iconNode);\nexport { __iconNode, TrainFront as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "TrainFront", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\train-front.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 3.1V7a4 4 0 0 0 8 0V3.1', key: '1v71zp' }],\n  ['path', { d: 'm9 15-1-1', key: '1yrq24' }],\n  ['path', { d: 'm15 15 1-1', key: '1t0d6s' }],\n  ['path', { d: 'M9 19c-2.8 0-5-2.2-5-5v-4a8 8 0 0 1 16 0v4c0 2.8-2.2 5-5 5Z', key: '1p0hjs' }],\n  ['path', { d: 'm8 19-2 3', key: '13i0xs' }],\n  ['path', { d: 'm16 19 2 3', key: 'xo31yx' }],\n];\n\n/**\n * @component @name TrainFront\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzLjFWN2E0IDQgMCAwIDAgOCAwVjMuMSIgLz4KICA8cGF0aCBkPSJtOSAxNS0xLTEiIC8+CiAgPHBhdGggZD0ibTE1IDE1IDEtMSIgLz4KICA8cGF0aCBkPSJNOSAxOWMtMi44IDAtNS0yLjItNS01di00YTggOCAwIDAgMSAxNiAwdjRjMCAyLjgtMi4yIDUtNSA1WiIgLz4KICA8cGF0aCBkPSJtOCAxOS0yIDMiIC8+CiAgPHBhdGggZD0ibTE2IDE5IDIgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/train-front\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrainFront = createLucideIcon('train-front', __iconNode);\n\nexport default TrainFront;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAA8BC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAA+DC,GAAA,EAAK;AAAA,CAAU,GAC5F,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAaC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAQ;EAAED,CAAA,EAAG;EAAcC,GAAA,EAAK;AAAA,CAAU,EAC7C;AAaA,MAAMC,UAAA,GAAaC,gBAAA,CAAiB,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}