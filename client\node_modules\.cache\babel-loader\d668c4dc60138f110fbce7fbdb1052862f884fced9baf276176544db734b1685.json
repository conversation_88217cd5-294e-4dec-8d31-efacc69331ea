{"ast": null, "code": "/**\n * @license lucide-react v0.541.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"6\",\n  key: \"1vlfrh\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"2\",\n  key: \"1c9p78\"\n}]];\nconst Target = createLucideIcon(\"target\", __iconNode);\nexport { __iconNode, Target as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "Target", "createLucideIcon"], "sources": ["C:\\Users\\<USER>\\Desktop\\hotel mangement\\client\\node_modules\\lucide-react\\src\\icons\\target.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Target\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Target = createLucideIcon('target', __iconNode);\n\nexport default Target;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAA,GAAuB,CAClC,CAAC,UAAU;EAAEC,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAMC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,UAAU;EAAEH,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,UAAU;EAAEH,EAAA,EAAI;EAAMC,EAAA,EAAI;EAAMC,CAAA,EAAG;EAAKC,GAAA,EAAK;AAAA,CAAU,EAC1D;AAaA,MAAMC,MAAA,GAASC,gBAAA,CAAiB,UAAUN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}