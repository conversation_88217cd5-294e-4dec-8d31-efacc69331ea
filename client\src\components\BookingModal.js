import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Calendar, Users, CreditCard, Check } from 'lucide-react';
import { useBooking } from '../contexts/BookingContext';

const BookingModal = ({ isOpen, onClose, room }) => {
  const {
    searchCriteria,
    guestInfo,
    currentStep,
    loading,
    error,
    setSearchCriteria,
    setSelectedRoom,
    setGuestInfo,
    setBookingStep,
    calculateTotalPrice,
    calculateNights,
    submitBooking,
    clearBooking,
    currentBooking
  } = useBooking();

  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    if (isOpen && room) {
      setSelectedRoom(room);
      setBookingStep(1);
    }
  }, [isOpen, room, setSelectedRoom, setBookingStep]);

  const validateStep1 = () => {
    const errors = {};
    const today = new Date().toISOString().split('T')[0];
    
    if (!searchCriteria.checkIn) {
      errors.checkIn = 'Check-in date is required';
    } else if (searchCriteria.checkIn < today) {
      errors.checkIn = 'Check-in date cannot be in the past';
    }
    
    if (!searchCriteria.checkOut) {
      errors.checkOut = 'Check-out date is required';
    } else if (searchCriteria.checkOut <= searchCriteria.checkIn) {
      errors.checkOut = 'Check-out date must be after check-in date';
    }
    
    if (searchCriteria.guests < 1 || searchCriteria.guests > room?.capacity) {
      errors.guests = `Number of guests must be between 1 and ${room?.capacity}`;
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const validateStep2 = () => {
    const errors = {};
    
    if (!guestInfo.firstName.trim()) {
      errors.firstName = 'First name is required';
    }
    
    if (!guestInfo.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }
    
    if (!guestInfo.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(guestInfo.email)) {
      errors.email = 'Email is invalid';
    }
    
    if (!guestInfo.phone.trim()) {
      errors.phone = 'Phone number is required';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (currentStep === 1 && validateStep1()) {
      setBookingStep(2);
    } else if (currentStep === 2 && validateStep2()) {
      setBookingStep(3);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setBookingStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (validateStep2()) {
      const result = await submitBooking();
      if (!result.success) {
        // Error is handled in context
      }
    }
  };

  const handleClose = () => {
    clearBooking();
    setFormErrors({});
    onClose();
  };

  const renderStep1 = () => (
    <div className="booking-step">
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
        Select Your Dates
      </h3>
      
      <div className="grid grid-cols-1 gap-4 mb-6" style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '1rem' }}>
        <div>
          <label className="form-label">Check-in Date</label>
          <input
            type="date"
            className="form-input"
            value={searchCriteria.checkIn}
            onChange={(e) => setSearchCriteria({ checkIn: e.target.value })}
            min={new Date().toISOString().split('T')[0]}
          />
          {formErrors.checkIn && (
            <p className="text-red-500 text-sm mt-1">{formErrors.checkIn}</p>
          )}
        </div>
        
        <div>
          <label className="form-label">Check-out Date</label>
          <input
            type="date"
            className="form-input"
            value={searchCriteria.checkOut}
            onChange={(e) => setSearchCriteria({ checkOut: e.target.value })}
            min={searchCriteria.checkIn || new Date().toISOString().split('T')[0]}
          />
          {formErrors.checkOut && (
            <p className="text-red-500 text-sm mt-1">{formErrors.checkOut}</p>
          )}
        </div>
        
        <div>
          <label className="form-label">Number of Guests</label>
          <select
            className="form-select"
            value={searchCriteria.guests}
            onChange={(e) => setSearchCriteria({ guests: parseInt(e.target.value) })}
          >
            {Array.from({ length: room?.capacity || 4 }, (_, i) => (
              <option key={i + 1} value={i + 1}>
                {i + 1} Guest{i > 0 ? 's' : ''}
              </option>
            ))}
          </select>
          {formErrors.guests && (
            <p className="text-red-500 text-sm mt-1">{formErrors.guests}</p>
          )}
        </div>
      </div>
      
      {calculateNights() > 0 && (
        <div className="booking-summary">
          <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg" style={{ backgroundColor: 'var(--gray-50)' }}>
            <div>
              <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                {calculateNights()} night{calculateNights() > 1 ? 's' : ''} × ${room?.price}
              </p>
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                {searchCriteria.guests} guest{searchCriteria.guests > 1 ? 's' : ''}
              </p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold" style={{ color: 'var(--primary-600)' }}>
                ${calculateTotalPrice()}
              </p>
              <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Total</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderStep2 = () => (
    <div className="booking-step">
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
        Guest Information
      </h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4" style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
        <div>
          <label className="form-label">First Name</label>
          <input
            type="text"
            className="form-input"
            value={guestInfo.firstName}
            onChange={(e) => setGuestInfo({ firstName: e.target.value })}
            placeholder="Enter first name"
          />
          {formErrors.firstName && (
            <p className="text-red-500 text-sm mt-1">{formErrors.firstName}</p>
          )}
        </div>
        
        <div>
          <label className="form-label">Last Name</label>
          <input
            type="text"
            className="form-input"
            value={guestInfo.lastName}
            onChange={(e) => setGuestInfo({ lastName: e.target.value })}
            placeholder="Enter last name"
          />
          {formErrors.lastName && (
            <p className="text-red-500 text-sm mt-1">{formErrors.lastName}</p>
          )}
        </div>
      </div>
      
      <div className="mb-4">
        <label className="form-label">Email Address</label>
        <input
          type="email"
          className="form-input"
          value={guestInfo.email}
          onChange={(e) => setGuestInfo({ email: e.target.value })}
          placeholder="Enter email address"
        />
        {formErrors.email && (
          <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
        )}
      </div>
      
      <div className="mb-4">
        <label className="form-label">Phone Number</label>
        <input
          type="tel"
          className="form-input"
          value={guestInfo.phone}
          onChange={(e) => setGuestInfo({ phone: e.target.value })}
          placeholder="Enter phone number"
        />
        {formErrors.phone && (
          <p className="text-red-500 text-sm mt-1">{formErrors.phone}</p>
        )}
      </div>
      
      <div className="mb-6">
        <label className="form-label">Special Requests (Optional)</label>
        <textarea
          className="form-textarea"
          value={guestInfo.specialRequests}
          onChange={(e) => setGuestInfo({ specialRequests: e.target.value })}
          placeholder="Any special requests or preferences..."
          rows={3}
        />
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="booking-step">
      <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
        Booking Summary
      </h3>
      
      <div className="booking-summary-card p-6 border rounded-lg mb-6" style={{ border: '1px solid var(--border-color)' }}>
        <div className="mb-4">
          <h4 className="font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>{room?.name}</h4>
          <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>{room?.description}</p>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4" style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
          <div>
            <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Check-in</p>
            <p style={{ color: 'var(--text-primary)' }}>{new Date(searchCriteria.checkIn).toLocaleDateString()}</p>
          </div>
          <div>
            <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Check-out</p>
            <p style={{ color: 'var(--text-primary)' }}>{new Date(searchCriteria.checkOut).toLocaleDateString()}</p>
          </div>
        </div>
        
        <div className="mb-4">
          <p className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Guest</p>
          <p style={{ color: 'var(--text-primary)' }}>{guestInfo.firstName} {guestInfo.lastName}</p>
          <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>{guestInfo.email}</p>
        </div>
        
        <div className="border-t pt-4" style={{ borderTop: '1px solid var(--border-color)' }}>
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium" style={{ color: 'var(--text-primary)' }}>
                {calculateNights()} night{calculateNights() > 1 ? 's' : ''} × ${room?.price}
              </p>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold" style={{ color: 'var(--primary-600)' }}>
                ${calculateTotalPrice()}
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {error && (
        <div className="error-message p-3 bg-red-100 border border-red-300 rounded-lg mb-4">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}
    </div>
  );

  const renderStep4 = () => (
    <div className="booking-step text-center">
      <div className="success-icon mb-4">
        <Check style={{ width: '4rem', height: '4rem', color: 'var(--green-500)', margin: '0 auto' }} />
      </div>
      
      <h3 className="text-2xl font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
        Booking Confirmed!
      </h3>
      
      <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
        Your reservation has been successfully created.
      </p>
      
      {currentBooking && (
        <div className="booking-confirmation p-4 bg-green-50 border border-green-200 rounded-lg mb-6">
          <p className="font-semibold mb-2" style={{ color: 'var(--green-700)' }}>
            Booking ID: {currentBooking.id}
          </p>
          <p className="text-sm" style={{ color: 'var(--green-600)' }}>
            A confirmation email has been sent to {guestInfo.email}
          </p>
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        onClick={handleClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-90vh overflow-y-auto"
          style={{ backgroundColor: 'var(--bg-primary)', maxHeight: '90vh' }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex justify-between items-center p-6 border-b" style={{ borderBottom: '1px solid var(--border-color)' }}>
            <h2 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>
              {currentStep === 4 ? 'Booking Confirmed' : 'Book Your Stay'}
            </h2>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              style={{ backgroundColor: 'transparent' }}
            >
              <X style={{ width: '1.5rem', height: '1.5rem', color: 'var(--text-secondary)' }} />
            </button>
          </div>

          {/* Progress Steps */}
          {currentStep < 4 && (
            <div className="px-6 py-4 border-b" style={{ borderBottom: '1px solid var(--border-color)' }}>
              <div className="flex items-center justify-between">
                {[1, 2, 3].map((step) => (
                  <div key={step} className="flex items-center">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        step <= currentStep
                          ? 'bg-primary-600 text-white'
                          : 'bg-gray-200 text-gray-600'
                      }`}
                      style={{
                        backgroundColor: step <= currentStep ? 'var(--primary-600)' : 'var(--gray-200)',
                        color: step <= currentStep ? 'white' : 'var(--gray-600)'
                      }}
                    >
                      {step}
                    </div>
                    {step < 3 && (
                      <div
                        className="w-16 h-1 mx-2"
                        style={{
                          backgroundColor: step < currentStep ? 'var(--primary-600)' : 'var(--gray-200)'
                        }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Content */}
          <div className="p-6">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
          </div>

          {/* Footer */}
          {currentStep < 4 && (
            <div className="flex justify-between items-center p-6 border-t" style={{ borderTop: '1px solid var(--border-color)' }}>
              <button
                onClick={handleBack}
                className="btn btn-secondary"
                disabled={currentStep === 1}
                style={{ opacity: currentStep === 1 ? 0.5 : 1 }}
              >
                Back
              </button>
              
              <div className="flex gap-2">
                {currentStep < 3 ? (
                  <button onClick={handleNext} className="btn btn-primary">
                    Next
                  </button>
                ) : (
                  <button
                    onClick={handleSubmit}
                    className="btn btn-primary"
                    disabled={loading}
                    style={{ opacity: loading ? 0.7 : 1 }}
                  >
                    {loading ? 'Processing...' : 'Confirm Booking'}
                  </button>
                )}
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BookingModal;
