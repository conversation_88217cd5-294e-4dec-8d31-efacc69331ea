{"ast": null, "code": "import { MotionValueState } from '../MotionValueState.mjs';\nfunction createEffect(addValue) {\n  const stateCache = new WeakMap();\n  const subscriptions = [];\n  return (subject, values) => {\n    const state = stateCache.get(subject) ?? new MotionValueState();\n    stateCache.set(subject, state);\n    for (const key in values) {\n      const value = values[key];\n      const remove = addValue(subject, state, key, value);\n      subscriptions.push(remove);\n    }\n    return () => {\n      for (const cancel of subscriptions) cancel();\n    };\n  };\n}\nexport { createEffect };", "map": {"version": 3, "names": ["MotionValueState", "createEffect", "addValue", "stateCache", "WeakMap", "subscriptions", "subject", "values", "state", "get", "set", "key", "value", "remove", "push", "cancel"], "sources": ["C:/Users/<USER>/Desktop/hotel mangement/client/node_modules/motion-dom/dist/es/effects/utils/create-effect.mjs"], "sourcesContent": ["import { MotionValueState } from '../MotionValueState.mjs';\n\nfunction createEffect(addValue) {\n    const stateCache = new WeakMap();\n    const subscriptions = [];\n    return (subject, values) => {\n        const state = stateCache.get(subject) ?? new MotionValueState();\n        stateCache.set(subject, state);\n        for (const key in values) {\n            const value = values[key];\n            const remove = addValue(subject, state, key, value);\n            subscriptions.push(remove);\n        }\n        return () => {\n            for (const cancel of subscriptions)\n                cancel();\n        };\n    };\n}\n\nexport { createEffect };\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,yBAAyB;AAE1D,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAC5B,MAAMC,UAAU,GAAG,IAAIC,OAAO,CAAC,CAAC;EAChC,MAAMC,aAAa,GAAG,EAAE;EACxB,OAAO,CAACC,OAAO,EAAEC,MAAM,KAAK;IACxB,MAAMC,KAAK,GAAGL,UAAU,CAACM,GAAG,CAACH,OAAO,CAAC,IAAI,IAAIN,gBAAgB,CAAC,CAAC;IAC/DG,UAAU,CAACO,GAAG,CAACJ,OAAO,EAAEE,KAAK,CAAC;IAC9B,KAAK,MAAMG,GAAG,IAAIJ,MAAM,EAAE;MACtB,MAAMK,KAAK,GAAGL,MAAM,CAACI,GAAG,CAAC;MACzB,MAAME,MAAM,GAAGX,QAAQ,CAACI,OAAO,EAAEE,KAAK,EAAEG,GAAG,EAAEC,KAAK,CAAC;MACnDP,aAAa,CAACS,IAAI,CAACD,MAAM,CAAC;IAC9B;IACA,OAAO,MAAM;MACT,KAAK,MAAME,MAAM,IAAIV,aAAa,EAC9BU,MAAM,CAAC,CAAC;IAChB,CAAC;EACL,CAAC;AACL;AAEA,SAASd,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}